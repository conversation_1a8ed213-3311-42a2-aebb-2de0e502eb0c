const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRecurringTransactionDirect() {
  try {
    console.log('🧪 Testando criação direta de transação recorrente no banco...\n');

    // Dados de teste
    const testData = {
      description: 'Teste Direto',
      fixedAmount: 100.50,
      frequency: 'MONTHLY',
      startDate: new Date('2025-06-20'),
      endDate: new Date('2025-12-20'),
      type: 'EXPENSE',
      accountId: 'cmc4su96g0004ykdky7771mpd',
      categoryId: 'cmc4su99j000kykdk49j27hpa',
      userId: 'cmc4su95j0000ykdkblmwcrwf',
      isActive: true
    };

    console.log('Dados para criação:', JSON.stringify(testData, null, 2));

    // Tentar criar diretamente no banco
    const result = await prisma.recurringTransaction.create({
      data: testData
    });

    console.log('✅ Transação recorrente criada com sucesso!');
    console.log('Resultado:', JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('❌ Erro ao criar transação recorrente:', error.message);
    console.error('Detalhes do erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRecurringTransactionDirect();
