/*
  Warnings:

  - Added the required column `user_id` to the `recurring_transactions` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "recurring_transactions" ADD COLUMN     "user_id" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "goal_progress_history" (
    "id" TEXT NOT NULL,
    "goal_id" TEXT NOT NULL,
    "previous_amount" DECIMAL(15,2) NOT NULL,
    "new_amount" DECIMAL(15,2) NOT NULL,
    "amount_changed" DECIMAL(15,2) NOT NULL,
    "operation" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "goal_progress_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "goal_progress_history_goal_id_idx" ON "goal_progress_history"("goal_id");

-- CreateIndex
CREATE INDEX "goal_progress_history_created_at_idx" ON "goal_progress_history"("created_at");

-- AddForeignKey
ALTER TABLE "recurring_transactions" ADD CONSTRAINT "recurring_transactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "goal_progress_history" ADD CONSTRAINT "goal_progress_history_goal_id_fkey" FOREIGN KEY ("goal_id") REFERENCES "goals"("id") ON DELETE CASCADE ON UPDATE CASCADE;
