const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testReductionGoal() {
  try {
    console.log('🧪 Testando implementação de metas de redução...')
    
    // Buscar um usuário existente
    const user = await prisma.user.findFirst({
      where: { isActive: true }
    })
    
    if (!user) {
      console.log('❌ Nenhum usuário encontrado. Criando usuário de teste...')
      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Teste Redução',
          password: 'hashedpassword123',
          isActive: true
        }
      })
      console.log('✅ Usuário de teste criado:', testUser.id)
    }
    
    const userId = user?.id || testUser.id
    
    // Teste 1: Criar meta de REDUÇÃO
    console.log('\n1️⃣ Criando meta de REDUÇÃO...')
    const reductionGoal = await prisma.goal.create({
      data: {
        name: '<PERSON><PERSON><PERSON> de Crédito',
        goalType: 'REDUCTION',
        targetAmount: 0, // Meta é zerar
        currentAmount: 5000, // Dívida atual
        initialAmount: 8000, // Dívida inicial
        targetDate: new Date('2025-12-31'),
        userId: userId
      }
    })
    console.log('✅ Meta de redução criada:', {
      id: reductionGoal.id,
      name: reductionGoal.name,
      type: reductionGoal.goalType,
      initial: reductionGoal.initialAmount,
      current: reductionGoal.currentAmount,
      target: reductionGoal.targetAmount
    })
    
    // Teste 2: Criar meta de ACUMULAÇÃO para comparação
    console.log('\n2️⃣ Criando meta de ACUMULAÇÃO...')
    const accumulationGoal = await prisma.goal.create({
      data: {
        name: 'Viagem para Europa',
        goalType: 'ACCUMULATION',
        targetAmount: 15000,
        currentAmount: 3000,
        targetDate: new Date('2025-12-31'),
        userId: userId
      }
    })
    console.log('✅ Meta de acumulação criada:', {
      id: accumulationGoal.id,
      name: accumulationGoal.name,
      type: accumulationGoal.goalType,
      current: accumulationGoal.currentAmount,
      target: accumulationGoal.targetAmount
    })
    
    // Teste 3: Calcular progresso
    console.log('\n3️⃣ Testando cálculo de progresso...')
    
    // Progresso da meta de redução
    const reductionInitial = Number(reductionGoal.initialAmount)
    const reductionCurrent = Number(reductionGoal.currentAmount)
    const reductionProgress = ((reductionInitial - reductionCurrent) / reductionInitial) * 100
    const reductionRemaining = reductionCurrent
    
    console.log(`📉 Meta de Redução:`)
    console.log(`   - Valor inicial: R$ ${reductionInitial.toFixed(2)}`)
    console.log(`   - Valor atual: R$ ${reductionCurrent.toFixed(2)}`)
    console.log(`   - Progresso: ${reductionProgress.toFixed(1)}%`)
    console.log(`   - Restante a pagar: R$ ${reductionRemaining.toFixed(2)}`)
    
    // Progresso da meta de acumulação
    const accumulationCurrent = Number(accumulationGoal.currentAmount)
    const accumulationTarget = Number(accumulationGoal.targetAmount)
    const accumulationProgress = (accumulationCurrent / accumulationTarget) * 100
    const accumulationRemaining = accumulationTarget - accumulationCurrent
    
    console.log(`📈 Meta de Acumulação:`)
    console.log(`   - Valor atual: R$ ${accumulationCurrent.toFixed(2)}`)
    console.log(`   - Valor alvo: R$ ${accumulationTarget.toFixed(2)}`)
    console.log(`   - Progresso: ${accumulationProgress.toFixed(1)}%`)
    console.log(`   - Falta acumular: R$ ${accumulationRemaining.toFixed(2)}`)
    
    // Teste 4: Buscar todas as metas
    console.log('\n4️⃣ Buscando todas as metas...')
    const allGoals = await prisma.goal.findMany({
      where: { 
        userId: userId,
        deletedAt: null 
      },
      select: {
        id: true,
        name: true,
        goalType: true,
        targetAmount: true,
        currentAmount: true,
        initialAmount: true
      }
    })
    
    console.log('📋 Metas encontradas:')
    allGoals.forEach(goal => {
      console.log(`  - ${goal.name} (${goal.goalType})`)
      if (goal.goalType === 'REDUCTION') {
        console.log(`    Inicial: R$ ${Number(goal.initialAmount || 0).toFixed(2)}, Atual: R$ ${Number(goal.currentAmount).toFixed(2)}`)
      } else {
        console.log(`    Atual: R$ ${Number(goal.currentAmount).toFixed(2)}, Meta: R$ ${Number(goal.targetAmount).toFixed(2)}`)
      }
    })
    
    // Limpeza
    console.log('\n🧹 Limpando dados de teste...')
    await prisma.goal.deleteMany({
      where: {
        id: {
          in: [reductionGoal.id, accumulationGoal.id]
        }
      }
    })
    console.log('✅ Dados de teste removidos')
    
    console.log('\n🎉 Teste concluído com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testReductionGoal()
