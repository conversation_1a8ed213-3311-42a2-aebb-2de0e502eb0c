const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkGoals() {
  try {
    console.log('🔍 Verificando metas no banco de dados...')
    
    const goals = await prisma.goal.findMany({
      where: { deletedAt: null },
      select: {
        id: true,
        name: true,
        goalType: true,
        targetAmount: true,
        currentAmount: true,
        initialAmount: true
      }
    })
    
    console.log(`📊 Total de metas encontradas: ${goals.length}`)
    
    if (goals.length > 0) {
      console.log('\n📋 Detalhes das metas:')
      goals.forEach((goal, index) => {
        console.log(`${index + 1}. ${goal.name}`)
        console.log(`   - Tipo: ${goal.goalType}`)
        console.log(`   - Valor Alvo: ${goal.targetAmount}`)
        console.log(`   - Valor Atual: ${goal.currentAmount}`)
        console.log(`   - Valor Inicial: ${goal.initialAmount || 'N/A'}`)
        console.log('')
      })
    } else {
      console.log('ℹ️  Nenhuma meta encontrada no banco de dados')
    }
    
    // Contar por tipo
    const accumulationCount = goals.filter(g => g.goalType === 'ACCUMULATION').length
    const reductionCount = goals.filter(g => g.goalType === 'REDUCTION').length
    
    console.log(`✅ Metas de ACUMULAÇÃO: ${accumulationCount}`)
    console.log(`🔻 Metas de REDUÇÃO: ${reductionCount}`)
    
  } catch (error) {
    console.error('❌ Erro ao verificar metas:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkGoals()
