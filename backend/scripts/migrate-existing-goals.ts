import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

/**
 * Script para migrar metas existentes para o novo modelo com GoalType
 * Define todas as metas existentes como 'ACCUMULATION' (padrão)
 */
async function migrateExistingGoals() {
  try {
    console.log('🚀 Iniciando migração de metas existentes...')

    // <PERSON>car todas as metas que não têm goalType definido (metas antigas)
    const existingGoals = await prisma.goal.findMany({
      where: {
        deletedAt: null
      },
      select: {
        id: true,
        name: true,
        goalType: true
      }
    })

    console.log(`📊 Encontradas ${existingGoals.length} metas para verificar`)

    // Contar metas que já são ACCUMULATION (por padrão da migration)
    const accumulationGoals = existingGoals.filter(goal => goal.goalType === 'ACCUMULATION')
    
    console.log(`✅ ${accumulationGoals.length} metas já estão definidas como ACCUMULATION`)

    if (accumulationGoals.length === existingGoals.length) {
      console.log('🎉 Todas as metas já estão migradas corretamente!')
      return
    }

    // Se houver metas sem tipo definido, atualizar para ACCUMULATION
    const goalsToUpdate = existingGoals.filter(goal => goal.goalType !== 'ACCUMULATION')
    
    if (goalsToUpdate.length > 0) {
      console.log(`🔄 Atualizando ${goalsToUpdate.length} metas para ACCUMULATION...`)
      
      await prisma.$transaction(async (tx) => {
        for (const goal of goalsToUpdate) {
          await tx.goal.update({
            where: { id: goal.id },
            data: { goalType: 'ACCUMULATION' }
          })
          console.log(`  ✓ Meta "${goal.name}" atualizada`)
        }
      })
    }

    console.log('✅ Migração concluída com sucesso!')
    
    // Verificação final
    const finalCount = await prisma.goal.count({
      where: {
        deletedAt: null,
        goalType: 'ACCUMULATION'
      }
    })
    
    console.log(`📈 Total de metas ACCUMULATION: ${finalCount}`)

  } catch (error) {
    console.error('❌ Erro durante a migração:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Executar o script se chamado diretamente
if (require.main === module) {
  migrateExistingGoals()
    .then(() => {
      console.log('🎯 Script de migração executado com sucesso!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Falha na execução do script:', error)
      process.exit(1)
    })
}

export { migrateExistingGoals }
