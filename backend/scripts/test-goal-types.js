const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testGoalTypes() {
  try {
    console.log('🧪 Testando tipos de metas...')
    
    // Teste 1: Criar meta de ACUMULAÇÃO
    console.log('\n1️⃣ Criando meta de ACUMULAÇÃO...')
    const accumulationGoal = await prisma.goal.create({
      data: {
        name: 'Teste Acumulação',
        goalType: 'ACCUMULATION',
        targetAmount: 10000,
        currentAmount: 2000,
        targetDate: new Date('2025-12-31')
      }
    })
    console.log('✅ Meta de acumulação criada:', accumulationGoal.id)
    
    // Teste 2: Criar meta de REDUÇÃO
    console.log('\n2️⃣ Criando meta de REDUÇÃO...')
    const reductionGoal = await prisma.goal.create({
      data: {
        name: 'Teste Redução de Dívida',
        goalType: 'REDUCTION',
        targetAmount: 0,
        currentAmount: 5000,
        initialAmount: 8000,
        targetDate: new Date('2025-12-31')
      }
    })
    console.log('✅ Meta de redução criada:', reductionGoal.id)
    
    // Teste 3: Buscar todas as metas
    console.log('\n3️⃣ Buscando todas as metas...')
    const allGoals = await prisma.goal.findMany({
      where: { deletedAt: null },
      select: {
        id: true,
        name: true,
        goalType: true,
        targetAmount: true,
        currentAmount: true,
        initialAmount: true
      }
    })
    
    console.log('📋 Metas encontradas:')
    allGoals.forEach(goal => {
      console.log(`  - ${goal.name} (${goal.goalType})`)
      console.log(`    Target: ${goal.targetAmount}, Current: ${goal.currentAmount}, Initial: ${goal.initialAmount || 'N/A'}`)
    })
    
    // Teste 4: Calcular progresso
    console.log('\n4️⃣ Testando cálculo de progresso...')
    
    // Progresso da meta de acumulação
    const accProgress = calculateProgress(accumulationGoal)
    console.log(`📈 Acumulação: ${accProgress.percentage}% (${accProgress.remainingAmount} restante)`)
    
    // Progresso da meta de redução
    const redProgress = calculateProgress(reductionGoal)
    console.log(`📉 Redução: ${redProgress.percentage}% (${redProgress.remainingAmount} restante)`)
    
    // Limpeza
    console.log('\n🧹 Limpando dados de teste...')
    await prisma.goal.deleteMany({
      where: {
        id: {
          in: [accumulationGoal.id, reductionGoal.id]
        }
      }
    })
    console.log('✅ Dados de teste removidos')
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  } finally {
    await prisma.$disconnect()
  }
}

function calculateProgress(goal) {
  const currentAmount = Number(goal.currentAmount)
  const targetAmount = Number(goal.targetAmount)
  const goalType = goal.goalType || 'ACCUMULATION'
  
  if (goalType === 'REDUCTION') {
    const initialAmount = Number(goal.initialAmount || 0)
    if (initialAmount > 0) {
      const reducedAmount = initialAmount - currentAmount
      const percentage = (reducedAmount / initialAmount) * 100
      const remainingAmount = currentAmount
      return { percentage, remainingAmount }
    }
    return { percentage: 0, remainingAmount: currentAmount }
  } else {
    const percentage = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0
    const remainingAmount = Math.max(0, targetAmount - currentAmount)
    return { percentage, remainingAmount }
  }
}

testGoalTypes()
