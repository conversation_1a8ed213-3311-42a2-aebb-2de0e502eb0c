console.log('🧪 Teste simples iniciado...')

try {
  const { PrismaClient } = require('@prisma/client')
  console.log('✅ Prisma importado com sucesso')
  
  const prisma = new PrismaClient()
  console.log('✅ Cliente Prisma criado')
  
  // Teste básico
  prisma.user.count()
    .then(count => {
      console.log('✅ Conexão com banco funcionando. Usuários:', count)
      return prisma.goal.count()
    })
    .then(goalCount => {
      console.log('✅ Metas no banco:', goalCount)
      
      // Teste de criação de meta de redução
      return prisma.goal.create({
        data: {
          name: 'Teste Meta Redução',
          goalType: 'REDUCTION',
          targetAmount: 0,
          currentAmount: 1000,
          initialAmount: 2000,
          userId: 'test-user-id'
        }
      })
    })
    .then(goal => {
      console.log('✅ Meta de redução criada:', {
        id: goal.id,
        type: goal.goalType,
        initial: goal.initialAmount,
        current: goal.currentAmount
      })
      
      // Limpar teste
      return prisma.goal.delete({
        where: { id: goal.id }
      })
    })
    .then(() => {
      console.log('✅ Meta de teste removida')
      console.log('🎉 Teste concluído com sucesso!')
    })
    .catch(error => {
      console.error('❌ Erro:', error.message)
    })
    .finally(() => {
      prisma.$disconnect()
    })
    
} catch (error) {
  console.error('❌ Erro na importação:', error.message)
}
