import { Request, Response } from 'express';
import { z } from 'zod';
import { Prisma } from '@prisma/client';
import { TransactionService } from '../services/transaction.service';
import {
  CreateTransactionSchema,
  UpdateTransactionSchema,
  TransactionFiltersSchema
} from '../schemas/transaction.schemas';

export class TransactionController {
  private transactionService = new TransactionService();

  /**
   * Create a new transaction with installments
   * @route POST /api/v1/transactions
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      // Validate request body
      const result = CreateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Create transaction with installments
      const transaction = await this.transactionService.create(result.data as any);

      res.status(201).json({
        success: true,
        data: transaction,
        message: 'Transação criada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get transaction by ID with installments
   * @route GET /api/v1/transactions/:id
   */
  async findById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Get transaction with installments
      const transaction = await this.transactionService.findById(id);

      if (!transaction) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Transação não encontrada',
            code: 'TRANSACTION_NOT_FOUND'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Transação obtida com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update transaction and installments (DELETE + CREATE strategy)
   * @route PUT /api/v1/transactions/:id
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Validate request body
      const result = UpdateTransactionSchema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Dados inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Check if there's data to update
      if (Object.keys(result.data).length === 0) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Nenhum dado fornecido para atualização',
            code: 'NO_UPDATE_DATA'
          }
        });
        return;
      }

      // Update transaction with installments
      const transaction = await this.transactionService.update(id, result.data as any);

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Transação atualizada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete transaction and all installments
   * @route DELETE /api/v1/transactions/:id
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Delete transaction and installments
      await this.transactionService.delete(id);

      res.status(200).json({
        success: true,
        message: 'Transação deletada com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update installment status (paid/unpaid)
   * @route PATCH /api/v1/transactions/:id/installments/:installmentNumber
   */
  async updateInstallmentStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id, installmentNumber } = req.params;
      const { isPaid, paidAt } = req.body;

      // Validate installment number
      const installmentNum = parseInt(installmentNumber);
      if (isNaN(installmentNum) || installmentNum < 1) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Número da parcela inválido',
            code: 'INVALID_INSTALLMENT_NUMBER'
          }
        });
        return;
      }

      // Validate isPaid
      if (typeof isPaid !== 'boolean') {
        res.status(400).json({
          success: false,
          error: {
            message: 'Status de pagamento deve ser verdadeiro ou falso',
            code: 'INVALID_PAYMENT_STATUS'
          }
        });
        return;
      }

      // Update installment status
      const transaction = await this.transactionService.updateInstallmentStatus(
        id,
        installmentNum,
        isPaid,
        paidAt
      );

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'Status da parcela atualizado com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all transactions with filters and pagination
   * @route GET /api/v1/transactions
   */
  async findAll(req: Request, res: Response): Promise<void> {
    try {
      // Validate query parameters
      const result = TransactionFiltersSchema.safeParse(req.query);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Parâmetros de consulta inválidos',
            code: 'VALIDATION_ERROR',
            details: result.error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
        return;
      }

      // Get filtered transactions with installments
      const { transactions, summary } = await this.getAllTransactionsWithFilters(result.data);

      // Return in expected format
      res.status(200).json({
        success: true,
        data: {
          data: transactions,
          pagination: {
            page: result.data.page || 1,
            limit: result.data.limit || 100,
            total: transactions.length,
            totalPages: Math.ceil(transactions.length / (result.data.limit || 100))
          },
          summary
        },
        message: 'Transações obtidas com sucesso'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all transactions with filters considering installment dates
   */
  private async getAllTransactionsWithFilters(filters: any) {
    // Import prisma here to avoid circular dependency
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      // Build where clause for transactions
      const transactionWhere: any = { deletedAt: null };

      // Apply basic filters
      if (filters.description) {
        transactionWhere.description = {
          contains: filters.description,
          mode: 'insensitive'
        };
      }

      if (filters.type) {
        transactionWhere.type = filters.type;
      }

      if (filters.accountId) {
        transactionWhere.OR = [
          { accountId: filters.accountId },
          { destinationAccountId: filters.accountId }
        ];
      }

      if (filters.categoryId) {
        transactionWhere.categoryId = filters.categoryId;
      }

      // For date filters, we need to include transactions that have installments in the date range
      if (filters.startDate || filters.endDate) {
        transactionWhere.installments = {
          some: {}
        };

        if (filters.startDate) {
          transactionWhere.installments.some.dueDate = {
            gte: new Date(filters.startDate)
          };
        }

        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          endDate.setHours(23, 59, 59, 999); // End of day

          if (transactionWhere.installments.some.dueDate) {
            transactionWhere.installments.some.dueDate.lte = endDate;
          } else {
            transactionWhere.installments.some.dueDate = { lte: endDate };
          }
        }
      }

      // Get transactions with all related data
      const transactions = await prisma.transaction.findMany({
        where: transactionWhere,
        include: {
          installments: {
            orderBy: { installmentNumber: 'asc' }
          },
          account: true,
          category: true,
          destinationAccount: true,
          tags: {
            include: { tag: true }
          },
          members: {
            include: { familyMember: true }
          }
        },
        orderBy: { transactionDate: 'desc' }
      });

      // Calculate summary based on installments in the filtered period
      const summary = {
        totalIncome: 0,
        totalExpenses: 0,
        totalTransfers: 0,
        netAmount: 0
      };

      // Format transactions and calculate summary
      const formattedTransactions = transactions.map(transaction => {
        // Filter installments by date range if specified
        let relevantInstallments = transaction.installments;

        if (filters.startDate || filters.endDate) {
          relevantInstallments = transaction.installments.filter(installment => {
            const dueDate = new Date(installment.dueDate);

            if (filters.startDate && dueDate < new Date(filters.startDate)) {
              return false;
            }

            if (filters.endDate) {
              const endDate = new Date(filters.endDate);
              endDate.setHours(23, 59, 59, 999);
              if (dueDate > endDate) {
                return false;
              }
            }

            return true;
          });
        }

        // Apply amount filters to relevant installments
        if (filters.minAmount || filters.maxAmount) {
          relevantInstallments = relevantInstallments.filter(installment => {
            const amount = Number(installment.amount);

            if (filters.minAmount && amount < filters.minAmount) {
              return false;
            }

            if (filters.maxAmount && amount > filters.maxAmount) {
              return false;
            }

            return true;
          });
        }

        // Calculate summary for relevant installments (only paid ones)
        relevantInstallments.forEach(installment => {
          // Only count paid installments in the summary
          if (installment.isPaid) {
            const amount = Number(installment.amount);
            switch (transaction.type) {
              case 'INCOME':
                summary.totalIncome += amount;
                break;
              case 'EXPENSE':
                summary.totalExpenses += amount;
                break;
              case 'TRANSFER':
                summary.totalTransfers += amount;
                break;
            }
          }
        });

        // Format transaction (same as before)
        return {
          id: transaction.id,
          description: transaction.description,
          totalAmount: Number(transaction.totalAmount),
          totalInstallments: transaction.totalInstallments,
          transactionDate: transaction.transactionDate.toISOString().split('T')[0],
          type: transaction.type,
          accountId: transaction.accountId,
          categoryId: transaction.categoryId,
          destinationAccountId: transaction.destinationAccountId,
          exchangeRate: transaction.exchangeRate ? Number(transaction.exchangeRate) : undefined,
          isFuture: transaction.isFuture,
          sourceCurrency: transaction.sourceCurrency,
          destinationCurrency: transaction.destinationCurrency,
          sourceAmount: transaction.sourceAmount ? Number(transaction.sourceAmount) : undefined,
          destinationAmount: transaction.destinationAmount ? Number(transaction.destinationAmount) : undefined,
          transferReference: transaction.transferReference,
          createdAt: transaction.createdAt.toISOString(),
          updatedAt: transaction.updatedAt.toISOString(),

          // Format installments
          installments: transaction.installments.map(installment => ({
            id: installment.id,
            installmentNumber: installment.installmentNumber,
            amount: Number(installment.amount),
            dueDate: installment.dueDate.toISOString().split('T')[0],
            isPaid: installment.isPaid,
            paidAt: installment.paidAt?.toISOString(),
            description: installment.description
          })),

          // Format related data (same as before)
          account: {
            id: transaction.account.id,
            name: transaction.account.name,
            type: transaction.account.type,
            currency: transaction.account.currency
          },

          category: transaction.category ? {
            id: transaction.category.id,
            name: transaction.category.name,
            color: transaction.category.color
          } : undefined,

          destinationAccount: transaction.destinationAccount ? {
            id: transaction.destinationAccount.id,
            name: transaction.destinationAccount.name,
            type: transaction.destinationAccount.type,
            currency: transaction.destinationAccount.currency
          } : undefined,

          tags: transaction.tags.map(tagRelation => ({
            id: tagRelation.tag.id,
            name: tagRelation.tag.name,
            color: tagRelation.tag.color
          })),

          familyMembers: transaction.members.map(memberRelation => ({
            id: memberRelation.familyMember.id,
            name: memberRelation.familyMember.name,
            color: memberRelation.familyMember.color
          }))
        };
      });

      summary.netAmount = summary.totalIncome - summary.totalExpenses;

      return { transactions: formattedTransactions, summary };
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Get all transactions with installments (legacy method)
   */
  private async getAllTransactions() {
    // Import prisma here to avoid circular dependency
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      const transactions = await prisma.transaction.findMany({
        where: { deletedAt: null },
        include: {
          installments: {
            orderBy: { installmentNumber: 'asc' }
          },
          account: true,
          category: true,
          destinationAccount: true,
          tags: {
            include: { tag: true }
          },
          members: {
            include: { familyMember: true }
          }
        },
        orderBy: { transactionDate: 'desc' }
      });

      // Format transactions to match frontend expectations
      return transactions.map(transaction => ({
        id: transaction.id,
        description: transaction.description,
        totalAmount: Number(transaction.totalAmount),
        totalInstallments: transaction.totalInstallments,
        transactionDate: transaction.transactionDate.toISOString().split('T')[0],
        type: transaction.type,
        accountId: transaction.accountId,
        categoryId: transaction.categoryId,
        destinationAccountId: transaction.destinationAccountId,
        exchangeRate: transaction.exchangeRate ? Number(transaction.exchangeRate) : undefined,
        isFuture: transaction.isFuture,
        sourceCurrency: transaction.sourceCurrency,
        destinationCurrency: transaction.destinationCurrency,
        sourceAmount: transaction.sourceAmount ? Number(transaction.sourceAmount) : undefined,
        destinationAmount: transaction.destinationAmount ? Number(transaction.destinationAmount) : undefined,
        transferReference: transaction.transferReference,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString(),

        // Format installments
        installments: transaction.installments.map(installment => ({
          id: installment.id,
          installmentNumber: installment.installmentNumber,
          amount: Number(installment.amount),
          dueDate: installment.dueDate.toISOString().split('T')[0],
          isPaid: installment.isPaid,
          paidAt: installment.paidAt?.toISOString(),
          description: installment.description
        })),

        // Format related data
        account: {
          id: transaction.account.id,
          name: transaction.account.name,
          type: transaction.account.type,
          currency: transaction.account.currency
        },

        category: transaction.category ? {
          id: transaction.category.id,
          name: transaction.category.name,
          color: transaction.category.color
        } : undefined,

        destinationAccount: transaction.destinationAccount ? {
          id: transaction.destinationAccount.id,
          name: transaction.destinationAccount.name,
          type: transaction.destinationAccount.type,
          currency: transaction.destinationAccount.currency
        } : undefined,

        // Format tags - flatten the nested structure
        tags: transaction.tags.map(tagRelation => ({
          id: tagRelation.tag.id,
          name: tagRelation.tag.name,
          color: tagRelation.tag.color
        })),

        // Format family members - flatten the nested structure
        familyMembers: transaction.members.map(memberRelation => ({
          id: memberRelation.familyMember.id,
          name: memberRelation.familyMember.name,
          color: memberRelation.familyMember.color
        }))
      }));
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * Handle errors and send appropriate response
   */
  private handleError(error: unknown, res: Response): void {
    console.error('Transaction Controller Error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        }
      });
      return;
    }

    // Handle Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          res.status(409).json({
            success: false,
            error: {
              message: 'Violação de restrição única',
              code: 'UNIQUE_CONSTRAINT_VIOLATION'
            }
          });
          return;
        case 'P2003':
          res.status(400).json({
            success: false,
            error: {
              message: 'Violação de chave estrangeira',
              code: 'FOREIGN_KEY_CONSTRAINT_VIOLATION'
            }
          });
          return;
        case 'P2025':
          res.status(404).json({
            success: false,
            error: {
              message: 'Registro não encontrado',
              code: 'RECORD_NOT_FOUND'
            }
          });
          return;
      }
    }

    // Handle application errors
    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('não encontrado') || error.message.includes('não encontrada')) {
        res.status(404).json({
          success: false,
          error: {
            message: error.message,
            code: 'TRANSACTION_NOT_FOUND'
          }
        });
        return;
      }

      res.status(400).json({
        success: false,
        error: {
          message: error.message,
          code: 'TRANSACTION_ERROR'
        }
      });
      return;
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    });
  }
}

export const transactionController = new TransactionController();
