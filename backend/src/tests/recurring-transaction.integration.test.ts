import request from 'supertest';
import app from '../index';
import prisma from '../lib/prisma';
import { RecurrenceFrequency, TransactionType } from '@prisma/client';
import jwt from 'jsonwebtoken';

describe('Recurring Transactions API Integration Tests', () => {
  let authToken: string;
  let testUserId: string;
  let testAccountId: string;
  let testCategoryId: string;
  let testRecurringTransactionId: string;

  beforeAll(async () => {
    // Create test user
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Test User'
      }
    });
    testUserId = testUser.id;

    // Generate JWT token
    authToken = jwt.sign(
      { userId: testUser.id, email: testUser.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );

    // Create test account
    const testAccount = await prisma.account.create({
      data: {
        name: 'Test Account',
        type: 'CHECKING',
        currency: 'BRL',
        currentBalance: 1000.00
      }
    });
    testAccountId = testAccount.id;

    // Create test category
    const testCategory = await prisma.category.create({
      data: {
        name: 'Test Category',
        color: '#FF0000'
      }
    });
    testCategoryId = testCategory.id;
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.recurringTransaction.deleteMany({
      where: { accountId: testAccountId }
    });
    await prisma.transaction.deleteMany({
      where: { accountId: testAccountId }
    });
    await prisma.category.delete({ where: { id: testCategoryId } });
    await prisma.account.delete({ where: { id: testAccountId } });
    await prisma.user.delete({ where: { id: testUserId } });
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up recurring transactions before each test
    await prisma.recurringTransaction.deleteMany({
      where: { accountId: testAccountId }
    });
  });

  describe('POST /api/v1/recurring-transactions', () => {
    const validRecurringTransactionData = {
      description: 'Test Recurring Transaction',
      fixedAmount: 100.00,
      frequency: RecurrenceFrequency.MONTHLY,
      startDate: '2024-01-01',
      type: TransactionType.EXPENSE,
      accountId: '',
      categoryId: '',
      isActive: true
    };

    beforeEach(() => {
      validRecurringTransactionData.accountId = testAccountId;
      validRecurringTransactionData.categoryId = testCategoryId;
    });

    it('should create a recurring transaction successfully', async () => {
      const response = await request(app)
        .post('/api/v1/recurring-transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(validRecurringTransactionData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.description).toBe(validRecurringTransactionData.description);
      expect(response.body.data.fixedAmount).toBe(validRecurringTransactionData.fixedAmount);
      expect(response.body.data.frequency).toBe(validRecurringTransactionData.frequency);

      testRecurringTransactionId = response.body.data.id;
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        ...validRecurringTransactionData,
        fixedAmount: undefined,
        percentageAmount: undefined // Both amounts missing
      };

      const response = await request(app)
        .post('/api/v1/recurring-transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });

    it('should return 401 without authentication', async () => {
      await request(app)
        .post('/api/v1/recurring-transactions')
        .send(validRecurringTransactionData)
        .expect(401);
    });

    it('should create recurring transaction with percentage amount', async () => {
      const percentageData = {
        ...validRecurringTransactionData,
        fixedAmount: undefined,
        percentageAmount: 5.0,
        type: TransactionType.INCOME
      };

      const response = await request(app)
        .post('/api/v1/recurring-transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(percentageData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.percentageAmount).toBe(5.0);
      expect(response.body.data.fixedAmount).toBeUndefined();
    });
  });

  describe('GET /api/v1/recurring-transactions', () => {
    beforeEach(async () => {
      // Create test recurring transactions
      await prisma.recurringTransaction.createMany({
        data: [
          {
            description: 'Monthly Salary',
            fixedAmount: 5000.00,
            frequency: RecurrenceFrequency.MONTHLY,
            startDate: new Date('2024-01-01'),
            type: TransactionType.INCOME,
            accountId: testAccountId,
            categoryId: testCategoryId,
            isActive: true,
            userId: testUserId
          },
          {
            description: 'Weekly Groceries',
            fixedAmount: 200.00,
            frequency: RecurrenceFrequency.WEEKLY,
            startDate: new Date('2024-01-01'),
            type: TransactionType.EXPENSE,
            accountId: testAccountId,
            categoryId: testCategoryId,
            isActive: true,
            userId: testUserId
          },
          {
            description: 'Inactive Transaction',
            fixedAmount: 50.00,
            frequency: RecurrenceFrequency.DAILY,
            startDate: new Date('2024-01-01'),
            type: TransactionType.EXPENSE,
            accountId: testAccountId,
            categoryId: testCategoryId,
            isActive: false,
            userId: testUserId
          }
        ]
      });
    });

    it('should return paginated recurring transactions', async () => {
      const response = await request(app)
        .get('/api/v1/recurring-transactions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(3);
      expect(response.body.pagination.total).toBe(3);
      expect(response.body.pagination.page).toBe(1);
    });

    it('should filter by active status', async () => {
      const response = await request(app)
        .get('/api/v1/recurring-transactions?isActive=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data.every((rt: any) => rt.isActive)).toBe(true);
    });

    it('should filter by frequency', async () => {
      const response = await request(app)
        .get(`/api/v1/recurring-transactions?frequency=${RecurrenceFrequency.MONTHLY}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].frequency).toBe(RecurrenceFrequency.MONTHLY);
    });

    it('should apply pagination', async () => {
      const response = await request(app)
        .get('/api/v1/recurring-transactions?page=1&limit=2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination.limit).toBe(2);
      expect(response.body.pagination.totalPages).toBe(2);
    });
  });

  describe('GET /api/v1/recurring-transactions/:id', () => {
    let recurringTransactionId: string;

    beforeEach(async () => {
      const rt = await prisma.recurringTransaction.create({
        data: {
          description: 'Test RT for Get By ID',
          fixedAmount: 150.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: testAccountId,
          categoryId: testCategoryId,
          isActive: true,
          userId: testUserId
        }
      });
      recurringTransactionId = rt.id;
    });

    it('should return recurring transaction by ID', async () => {
      const response = await request(app)
        .get(`/api/v1/recurring-transactions/${recurringTransactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(recurringTransactionId);
      expect(response.body.data.description).toBe('Test RT for Get By ID');
    });

    it('should return 404 for non-existent ID', async () => {
      const response = await request(app)
        .get('/api/v1/recurring-transactions/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('RECURRING_TRANSACTION_NOT_FOUND');
    });
  });

  describe('PUT /api/v1/recurring-transactions/:id', () => {
    let recurringTransactionId: string;

    beforeEach(async () => {
      const rt = await prisma.recurringTransaction.create({
        data: {
          description: 'Test RT for Update',
          fixedAmount: 200.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: testAccountId,
          categoryId: testCategoryId,
          isActive: true,
          userId: testUserId
        }
      });
      recurringTransactionId = rt.id;
    });

    it('should update recurring transaction successfully', async () => {
      const updateData = {
        description: 'Updated Description',
        fixedAmount: 250.00
      };

      const response = await request(app)
        .put(`/api/v1/recurring-transactions/${recurringTransactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.description).toBe('Updated Description');
      expect(response.body.data.fixedAmount).toBe(250.00);
      expect(response.body.data.version).toBe(2);
    });

    it('should return 400 for invalid update data', async () => {
      const invalidData = {
        fixedAmount: -100 // Negative amount
      };

      const response = await request(app)
        .put(`/api/v1/recurring-transactions/${recurringTransactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/v1/recurring-transactions/:id', () => {
    let recurringTransactionId: string;

    beforeEach(async () => {
      const rt = await prisma.recurringTransaction.create({
        data: {
          description: 'Test RT for Delete',
          fixedAmount: 100.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: testAccountId,
          categoryId: testCategoryId,
          isActive: true,
          userId: testUserId
        }
      });
      recurringTransactionId = rt.id;
    });

    it('should soft delete recurring transaction', async () => {
      const response = await request(app)
        .delete(`/api/v1/recurring-transactions/${recurringTransactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);

      // Verify soft delete
      const deletedRT = await prisma.recurringTransaction.findUnique({
        where: { id: recurringTransactionId }
      });
      expect(deletedRT?.isActive).toBe(false);
      expect(deletedRT?.deletedAt).toBeTruthy();
    });
  });

  describe('POST /api/v1/recurring-transactions/:id/installments', () => {
    let recurringTransactionId: string;

    beforeEach(async () => {
      const rt = await prisma.recurringTransaction.create({
        data: {
          description: 'Test RT for Installments',
          fixedAmount: 1200.00,
          frequency: RecurrenceFrequency.MONTHLY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: testAccountId,
          categoryId: testCategoryId,
          isActive: true,
          userId: testUserId
        }
      });
      recurringTransactionId = rt.id;
    });

    it('should create installments successfully', async () => {
      const installmentData = {
        numberOfInstallments: 12,
        description: 'Monthly Installment'
      };

      const response = await request(app)
        .post(`/api/v1/recurring-transactions/${recurringTransactionId}/installments`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(installmentData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.installments).toHaveLength(12);
      expect(response.body.data.parentTransactionId).toBeTruthy();
    });

    it('should return 400 for invalid installment data', async () => {
      const invalidData = {
        numberOfInstallments: 1 // Minimum is 2
      };

      const response = await request(app)
        .post(`/api/v1/recurring-transactions/${recurringTransactionId}/installments`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/v1/recurring-transactions/:id/next-execution', () => {
    let recurringTransactionId: string;

    beforeEach(async () => {
      const rt = await prisma.recurringTransaction.create({
        data: {
          description: 'Test RT for Next Execution',
          fixedAmount: 100.00,
          frequency: RecurrenceFrequency.DAILY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: testAccountId,
          categoryId: testCategoryId,
          isActive: true,
          userId: testUserId
        }
      });
      recurringTransactionId = rt.id;
    });

    it('should return next execution date', async () => {
      const response = await request(app)
        .get(`/api/v1/recurring-transactions/${recurringTransactionId}/next-execution`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.nextExecution).toBeTruthy();
      expect(new Date(response.body.data.nextExecution)).toBeInstanceOf(Date);
    });
  });

  describe('GET /api/v1/recurring-transactions/:id/should-execute', () => {
    let recurringTransactionId: string;

    beforeEach(async () => {
      const rt = await prisma.recurringTransaction.create({
        data: {
          description: 'Test RT for Should Execute',
          fixedAmount: 100.00,
          frequency: RecurrenceFrequency.DAILY,
          startDate: new Date('2024-01-01'),
          type: TransactionType.EXPENSE,
          accountId: testAccountId,
          categoryId: testCategoryId,
          isActive: true,
          userId: testUserId
        }
      });
      recurringTransactionId = rt.id;
    });

    it('should check if should execute today', async () => {
      const response = await request(app)
        .get(`/api/v1/recurring-transactions/${recurringTransactionId}/should-execute`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(typeof response.body.data.shouldExecute).toBe('boolean');
      expect(response.body.data.date).toBeTruthy();
    });
  });
});
