import { Prisma } from '@prisma/client';
import prisma from '../lib/prisma';
import {
  CreateFinancialGoalRequest,
  UpdateFinancialGoalRequest,
  UpdateGoalProgressRequest,
  FinancialGoalFilters,
  FinancialGoalResponse,
  PaginatedFinancialGoalsResponse,
  GoalProgress,
  GoalSummary,
  GoalProgressUpdateResponse
} from '../schemas/financial-goal.schemas';

export class FinancialGoalService {
  /**
   * Create a new financial goal
   */
  async create(data: CreateFinancialGoalRequest): Promise<FinancialGoalResponse> {
    try {
      // Validate that all family members exist and are not archived
      const familyMembers = await prisma.familyMember.findMany({
        where: {
          id: { in: data.familyMemberIds },
          deletedAt: null
        }
      });

      if (familyMembers.length !== data.familyMemberIds.length) {
        throw new Error('Um ou mais membros da família não foram encontrados ou foram arquivados');
      }

      // Create goal using transaction for data integrity
      const goal = await prisma.$transaction(async (tx) => {
        // Create the goal
        const newGoal = await tx.goal.create({
          data: {
            name: data.name,
            targetAmount: new Prisma.Decimal(data.targetAmount),
            currentAmount: new Prisma.Decimal(data.currentAmount),
            targetDate: data.targetDate
          }
        });

        // Create goal-member relationships
        await tx.goalMember.createMany({
          data: data.familyMemberIds.map(memberId => ({
            goalId: newGoal.id,
            familyMemberId: memberId
          }))
        });

        return newGoal;
      });

      // Fetch the complete goal with relationships
      const completeGoal = await prisma.goal.findUnique({
        where: { id: goal.id },
        include: {
          members: {
            include: {
              familyMember: true
            }
          },
          milestones: {
            orderBy: { targetDate: 'asc' }
          }
        }
      });

      if (!completeGoal) {
        throw new Error('Erro ao recuperar meta criada');
      }

      return this.formatGoalResponse(completeGoal);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao criar meta financeira');
    }
  }

  /**
   * Find all goals with filters and pagination
   */
  async findAll(filters: FinancialGoalFilters): Promise<PaginatedFinancialGoalsResponse> {
    try {
      const {
        familyMemberId,
        status,
        minTargetAmount,
        maxTargetAmount,
        targetDateFrom,
        targetDateTo,
        page,
        limit,
        sortBy,
        sortOrder,
        includeCompleted,
        includeMilestones
      } = filters;

      // Build where clause
      const where: Prisma.GoalWhereInput = {
        deletedAt: null,
        ...(familyMemberId && {
          members: {
            some: {
              familyMemberId: familyMemberId
            }
          }
        }),
        ...(minTargetAmount && {
          targetAmount: {
            gte: new Prisma.Decimal(minTargetAmount)
          }
        }),
        ...(maxTargetAmount && {
          targetAmount: {
            lte: new Prisma.Decimal(maxTargetAmount)
          }
        }),
        ...(targetDateFrom && {
          targetDate: {
            gte: targetDateFrom
          }
        }),
        ...(targetDateTo && {
          targetDate: {
            lte: targetDateTo
          }
        })
      };

      // Note: Status filtering will be done after fetching data
      // since Prisma doesn't support field-to-field comparisons directly

      // Build orderBy clause
      const orderBy: Prisma.GoalOrderByWithRelationInput = {};
      switch (sortBy) {
        case 'progress':
          // For progress sorting, we'll sort by currentAmount/targetAmount ratio
          // This is approximated by sorting by currentAmount for now
          orderBy.currentAmount = sortOrder;
          break;
        default:
          orderBy[sortBy] = sortOrder;
      }

      // Execute queries in parallel
      const [allGoals, totalBeforeStatusFilter] = await Promise.all([
        prisma.goal.findMany({
          where,
          include: {
            members: {
              include: {
                familyMember: true
              }
            },
            ...(includeMilestones && {
              milestones: {
                orderBy: { targetDate: 'asc' }
              }
            })
          },
          orderBy
        }),
        prisma.goal.count({ where })
      ]);

      // Apply status filter after fetching data
      let filteredGoals = allGoals;
      if (status !== 'all') {
        const now = new Date();
        filteredGoals = allGoals.filter(goal => {
          const currentAmount = Number(goal.currentAmount);
          const targetAmount = Number(goal.targetAmount);
          const isCompleted = currentAmount >= targetAmount;
          const isOverdue = goal.targetDate && new Date(goal.targetDate) < now && !isCompleted;

          switch (status) {
            case 'completed':
              return isCompleted;
            case 'overdue':
              return isOverdue;
            case 'active':
              return !isCompleted && !isOverdue;
            default:
              return true;
          }
        });
      }

      // Apply pagination to filtered results
      const total = filteredGoals.length;
      const paginatedGoals = filteredGoals.slice((page - 1) * limit, page * limit);

      // Format responses
      const formattedGoals: FinancialGoalResponse[] = paginatedGoals.map(goal =>
        this.formatGoalResponse(goal, includeMilestones)
      );

      return {
        data: formattedGoals,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao buscar metas financeiras');
    }
  }

  /**
   * Find goal by ID
   */
  async findById(id: string, includeMilestones: boolean = true): Promise<FinancialGoalResponse | null> {
    try {
      const goal = await prisma.goal.findFirst({
        where: {
          id,
          deletedAt: null
        },
        include: {
          members: {
            include: {
              familyMember: true
            }
          },
          ...(includeMilestones && {
            milestones: {
              orderBy: { targetDate: 'asc' }
            }
          })
        }
      });

      if (!goal) {
        return null;
      }

      return this.formatGoalResponse(goal, includeMilestones);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao buscar meta financeira');
    }
  }

  /**
   * Update goal
   */
  async update(id: string, data: UpdateFinancialGoalRequest): Promise<FinancialGoalResponse> {
    try {
      // Check if goal exists
      const existingGoal = await prisma.goal.findFirst({
        where: {
          id,
          deletedAt: null
        }
      });

      if (!existingGoal) {
        throw new Error('Meta financeira não encontrada');
      }

      // Validate family members if being updated
      if (data.familyMemberIds) {
        const familyMembers = await prisma.familyMember.findMany({
          where: {
            id: { in: data.familyMemberIds },
            deletedAt: null
          }
        });

        if (familyMembers.length !== data.familyMemberIds.length) {
          throw new Error('Um ou mais membros da família não foram encontrados ou foram arquivados');
        }
      }

      // Update goal using transaction
      const goal = await prisma.$transaction(async (tx) => {
        // Update the goal
        const updatedGoal = await tx.goal.update({
          where: { id },
          data: {
            ...(data.name && { name: data.name }),
            ...(data.targetAmount && { targetAmount: new Prisma.Decimal(data.targetAmount) }),
            ...(data.targetDate !== undefined && { targetDate: data.targetDate })
          }
        });

        // Update goal-member relationships if provided
        if (data.familyMemberIds) {
          // Delete existing relationships
          await tx.goalMember.deleteMany({
            where: { goalId: id }
          });

          // Create new relationships
          await tx.goalMember.createMany({
            data: data.familyMemberIds.map(memberId => ({
              goalId: id,
              familyMemberId: memberId
            }))
          });
        }

        return updatedGoal;
      });

      // Fetch the complete updated goal
      const completeGoal = await prisma.goal.findUnique({
        where: { id: goal.id },
        include: {
          members: {
            include: {
              familyMember: true
            }
          },
          milestones: {
            orderBy: { targetDate: 'asc' }
          }
        }
      });

      if (!completeGoal) {
        throw new Error('Erro ao recuperar meta atualizada');
      }

      return this.formatGoalResponse(completeGoal);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao atualizar meta financeira');
    }
  }

  /**
   * Update goal progress
   */
  async updateProgress(id: string, data: UpdateGoalProgressRequest): Promise<GoalProgressUpdateResponse> {
    try {
      // Check if goal exists
      const existingGoal = await prisma.goal.findFirst({
        where: {
          id,
          deletedAt: null
        }
      });

      if (!existingGoal) {
        throw new Error('Meta financeira não encontrada');
      }

      const previousAmount = Number(existingGoal.currentAmount);
      const goalType = existingGoal.goalType || 'ACCUMULATION';
      const targetAmount = Number(existingGoal.targetAmount);
      const initialAmount = existingGoal.initialAmount ? Number(existingGoal.initialAmount) : 0;
      let newAmount: number;

      // Calculate new amount based on operation
      switch (data.operation) {
        case 'add':
          newAmount = previousAmount + data.amount;
          break;
        case 'subtract':
          newAmount = Math.max(0, previousAmount - data.amount);
          break;
        case 'set':
          newAmount = data.amount;
          break;
        default:
          throw new Error('Operação inválida');
      }

      // Validate based on goal type
      if (goalType === 'REDUCTION') {
        // Para metas de redução: valor atual não pode exceder valor inicial
        if (newAmount > initialAmount) {
          throw new Error('Para metas de redução, valor atual não pode exceder o valor inicial');
        }
        // Valor não pode ser negativo
        if (newAmount < 0) {
          newAmount = 0;
        }
      } else {
        // Para metas de acumulação: validação original
        if (newAmount > targetAmount * 1.5) {
          throw new Error('Valor atual não pode exceder 150% do valor alvo');
        }
      }

      // Update goal using transaction
      const updatedGoal = await prisma.$transaction(async (tx) => {
        // Update the goal
        const goal = await tx.goal.update({
          where: { id },
          data: {
            currentAmount: new Prisma.Decimal(newAmount)
          },
          include: {
            members: {
              include: {
                familyMember: true
              }
            },
            milestones: {
              orderBy: { targetDate: 'asc' }
            }
          }
        });

        // Record progress history
        await tx.goalProgressHistory.create({
          data: {
            goalId: id,
            previousAmount: new Prisma.Decimal(previousAmount),
            newAmount: new Prisma.Decimal(newAmount),
            amountChanged: new Prisma.Decimal(newAmount - previousAmount),
            operation: data.operation,
            description: data.description
          }
        });

        return goal;
      });

      const amountChanged = newAmount - previousAmount;
      const progress = this.calculateGoalProgress(updatedGoal);

      return {
        goalId: id,
        previousAmount,
        newAmount,
        operation: data.operation,
        amountChanged,
        newProgress: progress,
        message: this.getProgressUpdateMessage(data.operation, data.amount, progress, goalType)
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao atualizar progresso da meta');
    }
  }

  /**
   * Delete goal (soft delete)
   */
  async delete(id: string): Promise<void> {
    try {
      const goal = await prisma.goal.findFirst({
        where: {
          id,
          deletedAt: null
        }
      });

      if (!goal) {
        throw new Error('Meta financeira não encontrada');
      }

      // Soft delete goal and related milestones
      await prisma.$transaction(async (tx) => {
        // Soft delete the goal
        await tx.goal.update({
          where: { id },
          data: {
            deletedAt: new Date()
          }
        });

        // Note: GoalMember relationships will be handled by cascade delete
        // Milestones are not soft deleted as they're part of the goal's history
      });
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao deletar meta financeira');
    }
  }

  /**
   * Get goal summary statistics
   */
  async getSummary(familyMemberId?: string): Promise<GoalSummary> {
    try {
      const where: Prisma.GoalWhereInput = {
        deletedAt: null,
        ...(familyMemberId && {
          members: {
            some: {
              familyMemberId: familyMemberId
            }
          }
        })
      };

      const [goals, aggregation] = await Promise.all([
        prisma.goal.findMany({
          where,
          select: {
            id: true,
            goalType: true,
            targetAmount: true,
            currentAmount: true,
            initialAmount: true,
            targetDate: true
          }
        }),
        prisma.goal.aggregate({
          where,
          _sum: {
            targetAmount: true,
            currentAmount: true
          },
          _count: true
        })
      ]);

      const now = new Date();
      let completedGoals = 0;
      let activeGoals = 0;
      let overdueGoals = 0;
      let totalProgress = 0;

      goals.forEach(goal => {
        const currentAmount = Number(goal.currentAmount);
        const targetAmount = Number(goal.targetAmount);
        const goalType = goal.goalType || 'ACCUMULATION';
        const initialAmount = goal.initialAmount ? Number(goal.initialAmount) : 0;

        let progress: number;
        let isCompleted: boolean;

        if (goalType === 'REDUCTION') {
          // Para metas de redução
          if (initialAmount > 0) {
            const reducedAmount = initialAmount - currentAmount;
            progress = (reducedAmount / initialAmount) * 100;
            isCompleted = currentAmount <= 0;
          } else {
            progress = 0;
            isCompleted = false;
          }
        } else {
          // Para metas de acumulação
          progress = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;
          isCompleted = currentAmount >= targetAmount;
        }

        totalProgress += progress;

        if (isCompleted) {
          completedGoals++;
        } else if (goal.targetDate && new Date(goal.targetDate) < now) {
          overdueGoals++;
        } else {
          activeGoals++;
        }
      });

      const totalGoals = goals.length;
      const averageProgress = totalGoals > 0 ? totalProgress / totalGoals : 0;

      return {
        totalGoals,
        completedGoals,
        activeGoals,
        overdueGoals,
        totalTargetAmount: Number(aggregation._sum.targetAmount || 0),
        totalCurrentAmount: Number(aggregation._sum.currentAmount || 0),
        overallProgress: Number(aggregation._sum.targetAmount) > 0
          ? (Number(aggregation._sum.currentAmount) / Number(aggregation._sum.targetAmount)) * 100
          : 0,
        averageProgress
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Erro interno do servidor ao calcular resumo das metas');
    }
  }

  /**
   * Format goal response
   */
  private formatGoalResponse(goal: any, includeMilestones: boolean = true): FinancialGoalResponse {
    const progress = this.calculateGoalProgress(goal);

    return {
      id: goal.id,
      name: goal.name,
      goalType: goal.goalType,
      targetAmount: Number(goal.targetAmount),
      currentAmount: Number(goal.currentAmount),
      initialAmount: goal.initialAmount ? Number(goal.initialAmount) : undefined,
      targetDate: goal.targetDate?.toISOString(),
      createdAt: goal.createdAt.toISOString(),
      updatedAt: goal.updatedAt.toISOString(),
      version: goal.version,
      progress,
      members: goal.members?.map((member: any) => ({
        id: member.familyMember.id,
        name: member.familyMember.name,
        color: member.familyMember.color
      })) || [],
      ...(includeMilestones && goal.milestones && {
        milestones: goal.milestones.map((milestone: any) => {
          // Calculate automatic completion status
          const autoCompleted = this.calculateMilestoneStatus(
            milestone,
            Number(goal.currentAmount),
            goal.goalType,
            goal.initialAmount ? Number(goal.initialAmount) : undefined
          )

          return {
            id: milestone.id,
            goalId: milestone.goalId,
            name: milestone.name,
            targetAmount: Number(milestone.targetAmount),
            targetDate: milestone.targetDate.toISOString(),
            isCompleted: autoCompleted, // ✅ Agora automático baseado em valor e data
            createdAt: milestone.createdAt.toISOString(),
            updatedAt: milestone.updatedAt.toISOString(),
            version: milestone.version
          }
        })
      })
    };
  }

  /**
   * Calculate automatic milestone completion status
   */
  private calculateMilestoneStatus(milestone: any, goalCurrentAmount: number, goalType: string = 'ACCUMULATION', goalInitialAmount?: number): boolean {
    const milestoneTargetAmount = Number(milestone.targetAmount)
    const milestoneTargetDate = new Date(milestone.targetDate)
    const today = new Date()

    const isWithinDeadline = today <= milestoneTargetDate

    let hasReachedAmount: boolean;

    if (goalType === 'REDUCTION') {
      // Para metas de redução: marco é atingido quando valor atual <= valor do marco
      hasReachedAmount = goalCurrentAmount <= milestoneTargetAmount
    } else {
      // Para metas de acumulação: marco é atingido quando valor atual >= valor do marco
      hasReachedAmount = goalCurrentAmount >= milestoneTargetAmount
    }

    return isWithinDeadline && hasReachedAmount
  }

  /**
   * Calculate goal progress
   */
  private calculateGoalProgress(goal: any): GoalProgress {
    const currentAmount = Number(goal.currentAmount);
    const targetAmount = Number(goal.targetAmount);
    const goalType = goal.goalType || 'ACCUMULATION';

    let percentage: number;
    let remainingAmount: number;
    let isCompleted: boolean;

    if (goalType === 'REDUCTION') {
      // Para metas de redução
      const initialAmount = Number(goal.initialAmount || 0);
      if (initialAmount > 0) {
        // Progresso = (valor inicial - valor atual) / valor inicial * 100
        const reducedAmount = initialAmount - currentAmount;
        percentage = (reducedAmount / initialAmount) * 100;
        remainingAmount = currentAmount; // O que ainda falta pagar
        isCompleted = currentAmount <= 0; // Meta concluída quando valor atual é 0 ou menor
      } else {
        percentage = 0;
        remainingAmount = currentAmount;
        isCompleted = false;
      }
    } else {
      // Para metas de acumulação (comportamento original)
      percentage = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;
      remainingAmount = Math.max(0, targetAmount - currentAmount);
      isCompleted = currentAmount >= targetAmount;
    }

    const now = new Date();
    const targetDate = goal.targetDate ? new Date(goal.targetDate) : null;
    const isOverdue = targetDate ? targetDate < now && !isCompleted : false;

    let daysRemaining: number | undefined;
    if (targetDate && !isCompleted) {
      const timeDiff = targetDate.getTime() - now.getTime();
      daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 3600 * 24)));
    }

    // Determine status
    let status: GoalProgress['status'];
    if (isCompleted) {
      status = 'completed';
    } else if (isOverdue) {
      status = 'overdue';
    } else if (percentage > 0) {
      status = 'in_progress';
    } else {
      status = 'not_started';
    }

    // Calculate milestone progress if milestones exist
    let milestoneProgress: GoalProgress['milestoneProgress'] | undefined;
    if (goal.milestones && goal.milestones.length > 0) {
      const completedMilestones = goal.milestones.filter((m: any) => m.isCompleted).length;
      const totalMilestones = goal.milestones.length;
      const nextMilestone = goal.milestones.find((m: any) => !m.isCompleted);

      milestoneProgress = {
        completed: completedMilestones,
        total: totalMilestones,
        ...(nextMilestone && {
          nextMilestone: {
            id: nextMilestone.id,
            goalId: nextMilestone.goalId,
            name: nextMilestone.name,
            targetAmount: Number(nextMilestone.targetAmount),
            targetDate: nextMilestone.targetDate.toISOString(),
            isCompleted: nextMilestone.isCompleted,
            createdAt: nextMilestone.createdAt.toISOString(),
            updatedAt: nextMilestone.updatedAt.toISOString(),
            version: nextMilestone.version
          }
        })
      };
    }

    return {
      percentage: Math.round(percentage * 100) / 100, // Round to 2 decimal places
      remainingAmount,
      isCompleted,
      isOverdue,
      daysRemaining,
      status,
      milestoneProgress
    };
  }

  /**
   * Generate progress update message
   */
  private getProgressUpdateMessage(
    operation: 'add' | 'subtract' | 'set',
    amount: number,
    progress: GoalProgress,
    goalType: string = 'ACCUMULATION'
  ): string {
    const formattedAmount = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount);

    if (goalType === 'REDUCTION') {
      switch (operation) {
        case 'add':
          return `Adicionado ${formattedAmount} à dívida. Progresso de redução: ${progress.percentage.toFixed(1)}%`;
        case 'subtract':
          return `Reduzido ${formattedAmount} da dívida. Progresso de redução: ${progress.percentage.toFixed(1)}%`;
        case 'set':
          return `Valor da dívida definido para ${formattedAmount}. Progresso de redução: ${progress.percentage.toFixed(1)}%`;
        default:
          return `Dívida atualizada. Progresso de redução: ${progress.percentage.toFixed(1)}%`;
      }
    } else {
      switch (operation) {
        case 'add':
          return `Adicionado ${formattedAmount} à meta. Progresso atual: ${progress.percentage.toFixed(1)}%`;
        case 'subtract':
          return `Subtraído ${formattedAmount} da meta. Progresso atual: ${progress.percentage.toFixed(1)}%`;
        case 'set':
          return `Valor atual definido para ${formattedAmount}. Progresso atual: ${progress.percentage.toFixed(1)}%`;
        default:
          return `Progresso atualizado. Progresso atual: ${progress.percentage.toFixed(1)}%`;
      }
    }
  }
}

// Export singleton instance
export const financialGoalService = new FinancialGoalService();
