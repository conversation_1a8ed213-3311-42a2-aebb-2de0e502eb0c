import prisma from '../lib/prisma';
import { TransactionType } from '@prisma/client';
import { normalizeText, tokenizeText, extractKeywords } from '../utils/text-similarity.utils';

/**
 * Interface for category frequency analysis
 */
export interface CategoryFrequency {
  categoryId: string;
  categoryName: string;
  frequency: number;
  averageAmount: number;
  totalTransactions: number;
  keywords: string[];
}

/**
 * Interface for transaction pattern analysis
 */
export interface TransactionPattern {
  description: string;
  categoryId: string;
  categoryName: string;
  amount: number;
  frequency: number;
  keywords: string[];
}

/**
 * Interface for value range analysis
 */
export interface ValueRangePattern {
  categoryId: string;
  categoryName: string;
  minAmount: number;
  maxAmount: number;
  averageAmount: number;
  transactionCount: number;
}

/**
 * Cache interface for optimization
 */
interface AnalysisCache {
  userId?: string;
  categoryFrequencies?: CategoryFrequency[];
  transactionPatterns?: TransactionPattern[];
  valueRanges?: ValueRangePattern[];
  keywordMap?: Map<string, string[]>; // keyword -> categoryIds
  lastUpdated?: Date;
}

/**
 * Service for analyzing historical transaction data to extract patterns
 * for category suggestion algorithms
 */
export class HistoricalAnalysisService {
  private cache: AnalysisCache = {};
  private readonly CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes

  /**
   * Get category frequency analysis for a user
   */
  async getCategoryFrequencies(userId?: string): Promise<CategoryFrequency[]> {
    // Check cache first
    if (this.isCacheValid(userId) && this.cache.categoryFrequencies) {
      return this.cache.categoryFrequencies;
    }

    const transactions = await prisma.transaction.findMany({
      where: {
        deletedAt: null,
        categoryId: { not: null },
        // Add user filter when user management is implemented
        // userId: userId
      },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        transactionDate: 'desc'
      },
      take: 1000 // Limit to recent transactions for performance
    });

    const categoryMap = new Map<string, {
      name: string;
      transactions: Array<{ description: string; amount: number }>;
    }>();

    // Group transactions by category
    transactions.forEach(transaction => {
      if (!transaction.category) return;

      const categoryId = transaction.category.id;
      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, {
          name: transaction.category.name,
          transactions: []
        });
      }

      categoryMap.get(categoryId)!.transactions.push({
        description: transaction.description,
        amount: Number(transaction.totalAmount)
      });
    });

    // Calculate frequencies and patterns
    const frequencies: CategoryFrequency[] = [];
    
    for (const [categoryId, data] of categoryMap.entries()) {
      const amounts = data.transactions.map(t => t.amount);
      const descriptions = data.transactions.map(t => t.description);
      
      // Extract common keywords from descriptions
      const allKeywords = descriptions.flatMap(desc => extractKeywords(desc, 3));
      const keywordFreq = new Map<string, number>();
      
      allKeywords.forEach(keyword => {
        keywordFreq.set(keyword, (keywordFreq.get(keyword) || 0) + 1);
      });
      
      const topKeywords = Array.from(keywordFreq.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([keyword]) => keyword);

      frequencies.push({
        categoryId,
        categoryName: data.name,
        frequency: data.transactions.length,
        averageAmount: amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length,
        totalTransactions: data.transactions.length,
        keywords: topKeywords
      });
    }

    // Sort by frequency (most used categories first)
    frequencies.sort((a, b) => b.frequency - a.frequency);

    // Update cache
    this.cache.categoryFrequencies = frequencies;
    this.cache.userId = userId;
    this.cache.lastUpdated = new Date();

    return frequencies;
  }

  /**
   * Get transaction patterns for similarity matching
   */
  async getTransactionPatterns(userId?: string): Promise<TransactionPattern[]> {
    // Check cache first
    if (this.isCacheValid(userId) && this.cache.transactionPatterns) {
      return this.cache.transactionPatterns;
    }

    const transactions = await prisma.transaction.findMany({
      where: {
        deletedAt: null,
        categoryId: { not: null },
        // Add user filter when user management is implemented
        // userId: userId
      },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        transactionDate: 'desc'
      },
      take: 500 // Limit for performance
    });

    // Group similar descriptions
    const descriptionMap = new Map<string, {
      categoryId: string;
      categoryName: string;
      amounts: number[];
      count: number;
    }>();

    transactions.forEach(transaction => {
      if (!transaction.category) return;

      const normalizedDesc = normalizeText(transaction.description);
      const key = normalizedDesc;

      if (!descriptionMap.has(key)) {
        descriptionMap.set(key, {
          categoryId: transaction.category.id,
          categoryName: transaction.category.name,
          amounts: [],
          count: 0
        });
      }

      const pattern = descriptionMap.get(key)!;
      pattern.amounts.push(Number(transaction.totalAmount));
      pattern.count++;
    });

    // Convert to patterns array
    const patterns: TransactionPattern[] = [];
    
    for (const [description, data] of descriptionMap.entries()) {
      if (data.count < 2) continue; // Only include patterns that appear multiple times

      const averageAmount = data.amounts.reduce((sum, amt) => sum + amt, 0) / data.amounts.length;
      const keywords = extractKeywords(description, 3);

      patterns.push({
        description,
        categoryId: data.categoryId,
        categoryName: data.categoryName,
        amount: averageAmount,
        frequency: data.count,
        keywords
      });
    }

    // Sort by frequency
    patterns.sort((a, b) => b.frequency - a.frequency);

    // Update cache
    this.cache.transactionPatterns = patterns;
    this.cache.userId = userId;
    this.cache.lastUpdated = new Date();

    return patterns;
  }

  /**
   * Get value range patterns for categories
   */
  async getValueRangePatterns(userId?: string): Promise<ValueRangePattern[]> {
    // Check cache first
    if (this.isCacheValid(userId) && this.cache.valueRanges) {
      return this.cache.valueRanges;
    }

    const result = await prisma.transaction.groupBy({
      by: ['categoryId'],
      where: {
        deletedAt: null,
        categoryId: { not: null },
        // Add user filter when user management is implemented
        // userId: userId
      },
      _min: {
        totalAmount: true
      },
      _max: {
        totalAmount: true
      },
      _avg: {
        totalAmount: true
      },
      _count: {
        id: true
      }
    });

    // Get category names
    const categoryIds = result.map(r => r.categoryId!);
    const categories = await prisma.category.findMany({
      where: {
        id: { in: categoryIds },
        deletedAt: null
      },
      select: {
        id: true,
        name: true
      }
    });

    const categoryNameMap = new Map(categories.map(c => [c.id, c.name]));

    const valueRanges: ValueRangePattern[] = result.map(r => ({
      categoryId: r.categoryId!,
      categoryName: categoryNameMap.get(r.categoryId!) || 'Unknown',
      minAmount: Number(r._min?.totalAmount || 0),
      maxAmount: Number(r._max?.totalAmount || 0),
      averageAmount: Number(r._avg?.totalAmount || 0),
      transactionCount: r._count?.id || 0
    }));

    // Update cache
    this.cache.valueRanges = valueRanges;
    this.cache.userId = userId;
    this.cache.lastUpdated = new Date();

    return valueRanges;
  }

  /**
   * Build keyword to category mapping for fast lookup
   */
  async getKeywordCategoryMap(userId?: string): Promise<Map<string, string[]>> {
    // Check cache first
    if (this.isCacheValid(userId) && this.cache.keywordMap) {
      return this.cache.keywordMap;
    }

    const frequencies = await this.getCategoryFrequencies(userId);
    const keywordMap = new Map<string, string[]>();

    frequencies.forEach(freq => {
      freq.keywords.forEach(keyword => {
        if (!keywordMap.has(keyword)) {
          keywordMap.set(keyword, []);
        }
        keywordMap.get(keyword)!.push(freq.categoryId);
      });
    });

    // Update cache
    this.cache.keywordMap = keywordMap;
    this.cache.userId = userId;
    this.cache.lastUpdated = new Date();

    return keywordMap;
  }

  /**
   * Find categories by keyword match
   */
  async findCategoriesByKeywords(description: string, userId?: string): Promise<string[]> {
    const keywordMap = await this.getKeywordCategoryMap(userId);
    const keywords = extractKeywords(description, 5);
    const categoryMatches = new Set<string>();

    keywords.forEach(keyword => {
      const categories = keywordMap.get(keyword);
      if (categories) {
        categories.forEach(categoryId => categoryMatches.add(categoryId));
      }
    });

    return Array.from(categoryMatches);
  }

  /**
   * Clear cache (useful for testing or when data changes significantly)
   */
  clearCache(): void {
    this.cache = {};
  }

  /**
   * Check if cache is valid for the given user
   */
  private isCacheValid(userId?: string): boolean {
    if (!this.cache.lastUpdated) return false;
    if (this.cache.userId !== userId) return false;
    
    const now = new Date();
    const cacheAge = now.getTime() - this.cache.lastUpdated.getTime();
    
    return cacheAge < this.CACHE_DURATION_MS;
  }
}

// Export singleton instance
export const historicalAnalysisService = new HistoricalAnalysisService();
