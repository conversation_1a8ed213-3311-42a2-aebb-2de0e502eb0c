import prisma from '../lib/prisma';
import { TransactionType } from '@prisma/client';

export interface CreateTransactionData {
  description: string;
  totalAmount: number;
  transactionDate: string | Date;
  type: TransactionType;
  accountId: string;
  categoryId?: string;
  destinationAccountId?: string;
  exchangeRate?: number;
  isFuture?: boolean;
  sourceCurrency?: string;
  destinationCurrency?: string;
  sourceAmount?: number;
  destinationAmount?: number;
  transferReference?: string;
  installments?: InstallmentData[];
  familyMemberIds?: string[];
  tagIds?: string[];
}

export interface InstallmentData {
  amount: number;
  dueDate: string | Date;
  isPaid?: boolean;
  description?: string;
}

export interface UpdateTransactionData extends Partial<CreateTransactionData> {
  id?: string;
}

export class TransactionService {
  /**
   * Create a new transaction with installments
   */
  async create(data: CreateTransactionData): Promise<any> {
    try {
      // 1. Validate inputs
      await this.validateInputs(data);

      // 2. Create main transaction
      const transaction = await prisma.transaction.create({
        data: {
          description: data.description,
          totalAmount: data.totalAmount,
          totalInstallments: data.installments?.length || 1,
          transactionDate: new Date(data.transactionDate),
          type: data.type,
          accountId: data.accountId,
          categoryId: data.categoryId,
          destinationAccountId: data.destinationAccountId,
          exchangeRate: data.exchangeRate,
          isFuture: data.isFuture || false,
          sourceCurrency: data.sourceCurrency,
          destinationCurrency: data.destinationCurrency,
          sourceAmount: data.sourceAmount,
          destinationAmount: data.destinationAmount,
          transferReference: data.transferReference
        }
      });

      // 3. Create installments
      const installments = data.installments || [{
        amount: data.totalAmount,
        dueDate: data.transactionDate,
        isPaid: false
      }];

      // Set automatic status based on due date
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      for (let i = 0; i < installments.length; i++) {
        const installmentData = installments[i];
        const dueDate = new Date(installmentData.dueDate);

        // Set isPaid to true if due date is today or in the past
        const shouldBePaid = dueDate <= today;
        const isPaid = installmentData.isPaid !== undefined ? installmentData.isPaid : shouldBePaid;

        await prisma.installment.create({
          data: {
            transactionId: transaction.id,
            installmentNumber: i + 1,
            amount: installmentData.amount,
            dueDate: dueDate,
            isPaid: isPaid,
            paidAt: isPaid ? dueDate : null,
            description: installmentData.description || `${data.description} - Parcela ${i + 1}/${installments.length}`
          }
        });
      }

      // 4. Add tags if provided
      if (data.tagIds && data.tagIds.length > 0) {
        await prisma.transactionTag.createMany({
          data: data.tagIds.map(tagId => ({
            transactionId: transaction.id,
            tagId
          }))
        });
      }

      // 5. Add family members if provided
      if (data.familyMemberIds && data.familyMemberIds.length > 0) {
        await prisma.transactionMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            transactionId: transaction.id,
            familyMemberId
          }))
        });
      }

      // 6. Update account balances if not future
      if (!data.isFuture) {
        await this.updateAccountBalance(data.accountId, data.type, data.totalAmount);
        
        if (data.type === TransactionType.TRANSFER && data.destinationAccountId) {
          await this.updateAccountBalance(data.destinationAccountId, TransactionType.INCOME, data.totalAmount);
        }
      }

      // 7. Return the created transaction with details
      return await this.findById(transaction.id);
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw new Error('Failed to create transaction');
    }
  }

  /**
   * Update transaction with new installments (DELETE + CREATE strategy)
   */
  async update(id: string, data: UpdateTransactionData): Promise<any> {
    try {
      // 1. Validate inputs
      await this.validateInputs(data);

      // 2. Get existing transaction
      const existingTransaction = await prisma.transaction.findUnique({
        where: { id },
        include: { installments: true }
      });

      if (!existingTransaction) {
        throw new Error('Transação não encontrada');
      }

      // 3. Update main transaction
      await prisma.transaction.update({
        where: { id },
        data: {
          description: data.description,
          totalAmount: data.totalAmount,
          totalInstallments: data.installments?.length || 1,
          transactionDate: data.transactionDate ? new Date(data.transactionDate) : undefined,
          categoryId: data.categoryId,
          destinationAccountId: data.destinationAccountId,
          exchangeRate: data.exchangeRate,
          isFuture: data.isFuture,
          sourceCurrency: data.sourceCurrency,
          destinationCurrency: data.destinationCurrency,
          sourceAmount: data.sourceAmount,
          destinationAmount: data.destinationAmount,
          transferReference: data.transferReference
        }
      });

      // 4. DELETE all existing installments
      await prisma.installment.deleteMany({
        where: { transactionId: id }
      });

      // 5. DELETE existing relationships
      await prisma.transactionTag.deleteMany({
        where: { transactionId: id }
      });
      
      await prisma.transactionMember.deleteMany({
        where: { transactionId: id }
      });

      // 6. CREATE new installments
      const installments = data.installments || [{
        amount: data.totalAmount || Number(existingTransaction.totalAmount),
        dueDate: data.transactionDate || existingTransaction.transactionDate,
        isPaid: false
      }];

      // Set automatic status based on due date
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      for (let i = 0; i < installments.length; i++) {
        const installmentData = installments[i];
        const dueDate = new Date(installmentData.dueDate);

        // Set isPaid to true if due date is today or in the past
        const shouldBePaid = dueDate <= today;
        const isPaid = installmentData.isPaid !== undefined ? installmentData.isPaid : shouldBePaid;

        await prisma.installment.create({
          data: {
            transactionId: id,
            installmentNumber: i + 1,
            amount: installmentData.amount,
            dueDate: dueDate,
            isPaid: isPaid,
            paidAt: isPaid ? dueDate : null,
            description: installmentData.description || `${data.description || existingTransaction.description} - Parcela ${i + 1}/${installments.length}`
          }
        });
      }

      // 7. CREATE new relationships
      if (data.tagIds && data.tagIds.length > 0) {
        await prisma.transactionTag.createMany({
          data: data.tagIds.map(tagId => ({
            transactionId: id,
            tagId
          }))
        });
      }

      if (data.familyMemberIds && data.familyMemberIds.length > 0) {
        await prisma.transactionMember.createMany({
          data: data.familyMemberIds.map(familyMemberId => ({
            transactionId: id,
            familyMemberId
          }))
        });
      }

      // 8. Return the updated transaction with details
      return await this.findById(id);
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw new Error('Failed to update transaction');
    }
  }

  /**
   * Get transaction by ID with all installments
   */
  async findById(id: string): Promise<any> {
    const transaction = await prisma.transaction.findUnique({
      where: { id, deletedAt: null },
      include: {
        installments: {
          orderBy: { installmentNumber: 'asc' }
        },
        account: true,
        category: true,
        destinationAccount: true,
        tags: {
          include: { tag: true }
        },
        members: {
          include: { familyMember: true }
        }
      }
    });

    return transaction;
  }

  /**
   * Delete transaction and all installments
   */
  async delete(id: string): Promise<void> {
    // Soft delete transaction
    await prisma.transaction.update({
      where: { id },
      data: { deletedAt: new Date() }
    });

    // Hard delete installments (cascade will handle this automatically)
    await prisma.installment.deleteMany({
      where: { transactionId: id }
    });
  }

  /**
   * Update installment status (paid/unpaid)
   */
  async updateInstallmentStatus(
    transactionId: string,
    installmentNumber: number,
    isPaid: boolean,
    paidAt?: string
  ): Promise<any> {
    try {
      // First, check if transaction exists
      const transaction = await prisma.transaction.findUnique({
        where: { id: transactionId, deletedAt: null },
        include: { installments: true }
      });

      if (!transaction) {
        throw new Error('Transação não encontrada');
      }

      // Find the specific installment
      const installment = transaction.installments.find(
        inst => inst.installmentNumber === installmentNumber
      );

      if (!installment) {
        throw new Error(`Parcela ${installmentNumber} não encontrada`);
      }

      // Update the installment status
      await prisma.installment.update({
        where: { id: installment.id },
        data: {
          isPaid,
          paidAt: isPaid ? (paidAt ? new Date(paidAt) : new Date()) : null
        }
      });

      // Return the updated transaction with all installments
      return await this.findById(transactionId);
    } catch (error) {
      console.error('Error updating installment status:', error);
      throw new Error('Falha ao atualizar status da parcela');
    }
  }

  /**
   * Validate input data
   */
  private async validateInputs(data: Partial<CreateTransactionData>): Promise<void> {
    // Validate account exists
    if (data.accountId) {
      const account = await prisma.account.findFirst({
        where: { id: data.accountId, deletedAt: null }
      });
      if (!account) {
        throw new Error('Conta não encontrada');
      }
    }

    // Validate category exists
    if (data.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: data.categoryId, deletedAt: null }
      });
      if (!category) {
        throw new Error('Categoria não encontrada');
      }
    }

    // Validate installments sum equals total amount
    if (data.installments && data.totalAmount) {
      const installmentsSum = data.installments.reduce((sum, inst) => sum + inst.amount, 0);
      if (Math.abs(installmentsSum - data.totalAmount) > 0.01) {
        throw new Error('A soma das parcelas deve ser igual ao valor total');
      }
    }
  }

  /**
   * Update account balance
   */
  private async updateAccountBalance(
    accountId: string,
    type: TransactionType,
    amount: number
  ): Promise<void> {
    const multiplier = type === TransactionType.INCOME ? 1 : -1;
    
    await prisma.account.update({
      where: { id: accountId },
      data: {
        currentBalance: {
          increment: amount * multiplier
        }
      }
    });
  }
}
