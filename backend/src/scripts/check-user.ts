import prisma from '../lib/prisma';

async function checkUser() {
  try {
    console.log('🔍 Checking users in database...\n');

    // Get all users
    const users = await prisma.user.findMany();
    console.log('Users found:', users.length);

    for (const user of users) {
      console.log(`
📧 Email: ${user.email}
👤 Name: ${user.name}
🔑 Password Hash: ${user.password}
✅ Active: ${user.isActive}
📅 Created: ${user.createdAt}
      `);
    }

  } catch (error) {
    console.error('❌ Error checking users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();
