import prisma from '../lib/prisma';
import * as bcrypt from 'bcrypt';

async function testPassword() {
  try {
    console.log('🔐 Testing password validation...\n');

    // Get the test user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log(`👤 Found user: ${user.email}`);
    console.log(`🔑 Stored hash: ${user.password}`);

    // Test different passwords
    const testPasswords = ['123456', 'password', 'test123'];
    
    for (const password of testPasswords) {
      const isValid = await bcrypt.compare(password, user.password);
      console.log(`🔐 Password '${password}': ${isValid ? '✅ VALID' : '❌ INVALID'}`);
    }

    // Generate a new hash for 123456 to compare
    console.log('\n🔄 Generating new hash for "123456":');
    const newHash = await bcrypt.hash('123456', 10);
    console.log(`New hash: ${newHash}`);
    
    const testNewHash = await bcrypt.compare('123456', newHash);
    console.log(`New hash validation: ${testNewHash ? '✅ VALID' : '❌ INVALID'}`);

    // Test the exact hash from seed
    const seedHash = '$2b$10$K7L/8Y1t85haFxndGPTw4.WC0BasOqfnQ7wweHFvKnmiWiJSNjeyK';
    const testSeedHash = await bcrypt.compare('123456', seedHash);
    console.log(`Seed hash validation: ${testSeedHash ? '✅ VALID' : '❌ INVALID'}`);

  } catch (error) {
    console.error('❌ Error testing password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPassword();
