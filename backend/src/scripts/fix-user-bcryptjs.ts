import prisma from '../lib/prisma';
import bcrypt from 'bcryptjs';

async function fixUserBcryptjs() {
  try {
    console.log('🔧 Fixing user password with bcryptjs...\n');

    // Generate correct hash for password "123456" using bcryptjs
    const correctHash = await bcrypt.hash('123456', 12); // Using same rounds as service
    console.log(`🔑 Generated correct hash with bcryptjs: ${correctHash}`);

    // Update the user with correct password hash
    const updatedUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: correctHash }
    });

    console.log(`✅ Updated user: ${updatedUser.email}`);

    // Test the password
    const isValid = await bcrypt.compare('123456', correctHash);
    console.log(`🔐 Password validation test: ${isValid ? '✅ VALID' : '❌ INVALID'}`);

    console.log('\n🎉 User password fixed successfully with bcryptjs!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 123456');

  } catch (error) {
    console.error('❌ Error fixing user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixUserBcryptjs();
