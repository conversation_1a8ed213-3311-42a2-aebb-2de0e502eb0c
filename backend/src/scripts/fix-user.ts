import prisma from '../lib/prisma';
import * as bcrypt from 'bcrypt';

async function fixUser() {
  try {
    console.log('🔧 Fixing user password...\n');

    // Generate correct hash for password "123456"
    const correctHash = await bcrypt.hash('123456', 10);
    console.log(`🔑 Generated correct hash: ${correctHash}`);

    // Update the user with correct password hash
    const updatedUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: correctHash }
    });

    console.log(`✅ Updated user: ${updatedUser.email}`);

    // Test the password
    const isValid = await bcrypt.compare('123456', correctHash);
    console.log(`🔐 Password validation test: ${isValid ? '✅ VALID' : '❌ INVALID'}`);

    console.log('\n🎉 User password fixed successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 123456');

  } catch (error) {
    console.error('❌ Error fixing user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixUser();
