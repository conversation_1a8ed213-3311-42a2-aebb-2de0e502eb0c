{"name": "personal-finance-backend", "version": "1.0.0", "description": "Backend API for Personal Finance Manager", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:advanced": "jest --testPathPattern=\"currency-conversion|transfer-transactions|installment-transactions|batch-processing\"", "seed:advanced": "tsx src/scripts/seed-advanced-features.ts", "test:acceptance": "tsx src/scripts/acceptance-tests.ts", "test:coverage": "jest --coverage", "test:data": "ts-node src/scripts/test-data.ts", "test:data:create": "ts-node src/scripts/test-data.ts create", "test:data:clean": "ts-node src/scripts/test-data.ts clean", "test:data:reset": "ts-node src/scripts/test-data.ts reset", "test:data:minimal": "ts-node src/scripts/test-data.ts minimal", "test:data:comprehensive": "ts-node src/scripts/test-data.ts comprehensive", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:performance:gates": "k6 run performance/scripts/quality-gates.js", "test:smoke": "jest --testPathPattern=smoke", "test:health": "jest --testPathPattern=health", "type-check": "tsc --noEmit", "security:scan": "npm audit --audit-level high", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:reset": "prisma migrate reset", "db:optimize": "tsx src/scripts/optimize-dashboard-indexes.ts", "cache:test": "tsx src/scripts/test-cache-system.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^5.7.0", "@types/bcrypt": "^5.0.2", "@types/ioredis": "^4.28.10", "@types/multer": "^1.4.12", "@types/node-schedule": "^2.1.7", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "node-schedule": "^2.1.1", "prisma": "^5.7.0", "redis": "^5.5.5", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/redis": "^4.0.10", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "commander": "^4.1.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "redis-memory-server": "^0.12.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}