const axios = require('axios');

async function testRecurringTransaction() {
  try {
    console.log('🧪 Testando API de Transações Recorrentes...\n');

    // 1. Login para obter token
    console.log('1. Fazendo login...');
    const loginResponse = await axios.post('http://localhost:3001/api/v1/auth/login', {
      email: '<EMAIL>',
      password: '123456'
    });

    if (loginResponse.status !== 200) {
      throw new Error('Login falhou');
    }

    const token = loginResponse.data.token;
    console.log('✅ Login realizado com sucesso');

    // 2. Buscar contas disponíveis
    console.log('\n2. Buscando contas...');
    const accountsResponse = await axios.get('http://localhost:3001/api/v1/accounts', {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (accountsResponse.data.data.length === 0) {
      throw new Error('Nenhuma conta encontrada');
    }

    const accountId = accountsResponse.data.data[0].id;
    console.log(`✅ Conta encontrada: ${accountId}`);

    // 3. Buscar categorias disponíveis
    console.log('\n3. Buscando categorias...');
    const categoriesResponse = await axios.get('http://localhost:3001/api/v1/categories', {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (categoriesResponse.data.data.length === 0) {
      throw new Error('Nenhuma categoria encontrada');
    }

    const categoryId = categoriesResponse.data.data[0].id;
    console.log(`✅ Categoria encontrada: ${categoryId}`);

    // 4. Criar transação recorrente
    console.log('\n4. Criando transação recorrente...');
    const recurringData = {
      description: 'Teste Transação Recorrente',
      fixedAmount: 100.50,
      frequency: 'MONTHLY',
      startDate: '2025-06-20T00:00:00.000Z',
      endDate: '2025-12-20T00:00:00.000Z',
      type: 'EXPENSE',
      accountId: accountId,
      categoryId: categoryId
    };

    console.log('Dados enviados:', JSON.stringify(recurringData, null, 2));

    const createResponse = await axios.post('http://localhost:3001/api/v1/recurring-transactions', recurringData, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (createResponse.status === 201) {
      console.log('✅ Transação recorrente criada com sucesso!');
      console.log('Resposta:', JSON.stringify(createResponse.data, null, 2));
    } else {
      console.log('❌ Falha ao criar transação recorrente');
      console.log('Status:', createResponse.status);
      console.log('Resposta:', createResponse.data);
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Dados:', error.response.data);
    }
  }
}

// Executar teste
testRecurringTransaction();
