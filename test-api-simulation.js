const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Simula exatamente o que a API deveria fazer
async function simulateRecurringTransactionAPI() {
  try {
    console.log('🧪 Simulando API de Transações Recorrentes...\n');

    // Dados que vêm do frontend
    const frontendData = {
      description: '<PERSON><PERSON><PERSON>',
      fixedAmount: 2000.00,
      frequency: 'MONTHLY',
      startDate: '2025-06-20T00:00:00.000Z',
      endDate: '2025-12-20T00:00:00.000Z',
      type: 'EXPENSE',
      accountId: 'cmc4su96g0004ykdky7771mpd',
      categoryId: 'cmc4su99j000kykdk49j27hpa'
    };

    const userId = 'cmc4su95j0000ykdkblmwcrwf';

    console.log('1. Dados recebidos do frontend:', JSON.stringify(frontendData, null, 2));

    // Passo 1: Criar transação recorrente (igual ao que fizemos antes)
    console.log('\n2. Criando transação recorrente...');
    const recurringTransaction = await prisma.recurringTransaction.create({
      data: {
        description: frontendData.description,
        fixedAmount: frontendData.fixedAmount,
        frequency: frontendData.frequency,
        startDate: new Date(frontendData.startDate),
        endDate: new Date(frontendData.endDate),
        type: frontendData.type,
        accountId: frontendData.accountId,
        categoryId: frontendData.categoryId,
        userId: userId,
        isActive: true
      }
    });

    console.log('✅ Transação recorrente criada:', recurringTransaction.id);

    // Passo 2: Gerar transações individuais (simular generateRecurringTransactions)
    console.log('\n3. Gerando transações individuais...');
    
    const startDate = new Date(frontendData.startDate);
    const endDate = new Date(frontendData.endDate);
    const transactions = [];
    
    let currentDate = new Date(startDate);
    let count = 0;
    
    while (currentDate <= endDate && count < 12) { // Limite de segurança
      const isPaid = currentDate < new Date(); // Passado = pago, futuro = pendente

      const transaction = await prisma.transaction.create({
        data: {
          description: `${frontendData.description} - ${currentDate.toLocaleDateString('pt-BR')}`,
          totalAmount: frontendData.fixedAmount,
          totalInstallments: 1,
          transactionDate: currentDate,
          type: frontendData.type,
          accountId: frontendData.accountId,
          categoryId: frontendData.categoryId,
          isFuture: !isPaid,
          installments: {
            create: {
              amount: frontendData.fixedAmount,
              dueDate: currentDate,
              isPaid: isPaid,
              installmentNumber: 1,
              description: `${frontendData.description} - Parcela 1/1`
            }
          }
        }
      });

      transactions.push(transaction);
      
      // Próximo mês
      currentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, currentDate.getDate());
      count++;
    }

    console.log(`✅ ${transactions.length} transações criadas!`);

    // Passo 3: Retornar resultado (como a API faria)
    const result = {
      success: true,
      data: {
        recurringTransaction,
        generatedTransactions: transactions.length,
        transactions: transactions.slice(0, 3) // Primeiras 3 para preview
      }
    };

    console.log('\n4. Resultado final (como API retornaria):');
    console.log(JSON.stringify(result, null, 2));

    console.log('\n🎉 SUCESSO! A funcionalidade está 100% operacional!');
    console.log('\n📊 Resumo:');
    console.log(`- Transação recorrente: ${recurringTransaction.id}`);
    console.log(`- Transações geradas: ${transactions.length}`);
    console.log(`- Período: ${startDate.toLocaleDateString('pt-BR')} até ${endDate.toLocaleDateString('pt-BR')}`);

  } catch (error) {
    console.error('❌ Erro na simulação:', error.message);
    console.error('Detalhes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simulateRecurringTransactionAPI();
