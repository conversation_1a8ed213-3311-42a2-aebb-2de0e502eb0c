import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { RecurringTransactionData } from '@/types/transaction.types'
import { toast } from 'sonner'

// Types for API responses
interface RecurringTransaction {
  id: string
  description: string
  fixedAmount: number
  frequency: string
  startDate: string
  endDate?: string
  type: string
  accountId: string
  categoryId?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  account: {
    id: string
    name: string
    type: string
    currency: string
  }
  category?: {
    id: string
    name: string
    color: string
  }
}

interface CreateRecurringTransactionResponse {
  success: boolean
  data: {
    recurringTransaction: RecurringTransaction
    generatedTransactions: number
    transactions: any[]
  }
}

interface ListRecurringTransactionsResponse {
  success: boolean
  data: RecurringTransaction[]
}

// API functions
const recurringTransactionApi = {
  create: async (data: RecurringTransactionData): Promise<CreateRecurringTransactionResponse> => {
    console.log('useRecurringTransactions - API call to /recurring-transactions with data:', data)
    const response = await api.post('/recurring-transactions', data)
    console.log('useRecurringTransactions - API response:', response.data)
    return response.data
  },

  list: async (): Promise<ListRecurringTransactionsResponse> => {
    const response = await api.get('/recurring-transactions')
    return response.data
  },

  delete: async (id: string): Promise<{ success: boolean; message: string }> => {
    const response = await api.delete(`/recurring-transactions/${id}`)
    return response.data
  }
}

// Hooks
export const useRecurringTransactions = () => {
  return useQuery({
    queryKey: ['recurring-transactions'],
    queryFn: recurringTransactionApi.list,
    select: (data) => data.data
  })
}

export const useCreateRecurringTransaction = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: recurringTransactionApi.create,
    onSuccess: (data) => {
      // Invalidate and refetch recurring transactions
      queryClient.invalidateQueries({ queryKey: ['recurring-transactions'] })
      
      // Also invalidate regular transactions since new ones were created
      queryClient.invalidateQueries({ queryKey: ['transactions'] })
      queryClient.invalidateQueries({ queryKey: ['dashboard'] })
      
      toast.success(
        `Transação recorrente criada com sucesso! ${data.data.generatedTransactions} transações foram geradas.`
      )
    },
    onError: (error: any) => {
      console.error('Error creating recurring transaction:', error)
      toast.error(
        error.response?.data?.error || 'Erro ao criar transação recorrente'
      )
    }
  })
}

export const useDeleteRecurringTransaction = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: recurringTransactionApi.delete,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['recurring-transactions'] })
      toast.success(data.message)
    },
    onError: (error: any) => {
      console.error('Error deleting recurring transaction:', error)
      toast.error(
        error.response?.data?.error || 'Erro ao desativar transação recorrente'
      )
    }
  })
}

// Export types for use in components
export type { RecurringTransaction, CreateRecurringTransactionResponse }
