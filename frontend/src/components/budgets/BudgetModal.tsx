import { Calculator } from 'lucide-react'
import { BudgetForm } from './BudgetForm'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import type { Budget, CreateBudgetRequest, UpdateBudgetRequest } from '@/types/budget.types'

interface BudgetModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateBudgetRequest | UpdateBudgetRequest) => void
  mode: 'create' | 'edit'
  budget?: Budget | null
  isLoading?: boolean
}

export function BudgetModal({ 
  open, 
  onOpenChange, 
  onSubmit, 
  mode, 
  budget, 
  isLoading = false 
}: BudgetModalProps) {
  const title = mode === 'create' ? 'Novo Orçamento' : 'Editar Orçamento'
  const submitText = mode === 'create' ? 'Criar Orçamento' : 'Salvar Alterações'

  const handleSubmit = (data: CreateBudgetRequest | UpdateBudgetRequest) => {
    onSubmit(data)
  }

  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold text-gradient-deep">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-deep shadow-soft">
              <Calculator className="h-4 w-4 text-white" />
            </div>
            {title}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {mode === 'create'
              ? 'Preencha os campos abaixo para criar um novo orçamento mensal por categoria'
              : 'Edite as informações do orçamento selecionado conforme necessário'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <BudgetForm
            mode={mode}
            budget={budget}
            onSubmit={handleSubmit}
            onCancel={handleClose}
            isLoading={isLoading}
            submitText={submitText}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
