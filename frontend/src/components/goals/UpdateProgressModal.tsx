import { useState } from 'react'
import { TrendingUp, TrendingDown, DollarSign } from 'lucide-react'
import { formatCurrency, parseCurrency } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import type { FinancialGoal, ProgressOperation } from '@/types/goal.types'

interface UpdateProgressModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (amount: number, operation: ProgressOperation, description?: string) => void
  goal: FinancialGoal
  isLoading?: boolean
}

export function UpdateProgressModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  goal, 
  isLoading = false 
}: UpdateProgressModalProps) {
  const [amount, setAmount] = useState('')
  const [operation, setOperation] = useState<ProgressOperation>('add')
  const [description, setDescription] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Reset errors
    setErrors({})
    
    // Validate amount
    const numericAmount = parseCurrency(amount)
    
    if (!amount || isNaN(numericAmount) || numericAmount <= 0) {
      setErrors({ amount: 'Valor deve ser maior que zero' })
      return
    }

    // Validate operation constraints based on goal type
    if (goal.goalType === 'REDUCTION') {
      // Para metas de redução
      const initialAmount = goal.initialAmount || 0

      if (operation === 'add' && (goal.currentAmount + numericAmount) > initialAmount) {
        setErrors({ amount: 'Valor total não pode exceder o valor inicial da dívida' })
        return
      }

      if (operation === 'subtract' && numericAmount > goal.currentAmount) {
        setErrors({ amount: 'Não é possível subtrair mais do que o valor atual' })
        return
      }

      if (operation === 'set' && numericAmount > initialAmount) {
        setErrors({ amount: 'Valor não pode ser maior que o valor inicial da dívida' })
        return
      }
    } else {
      // Para metas de acumulação (comportamento original)
      if (operation === 'subtract' && numericAmount > goal.currentAmount) {
        setErrors({ amount: 'Não é possível subtrair mais do que o valor atual' })
        return
      }

      if (operation === 'set' && numericAmount > goal.targetAmount) {
        setErrors({ amount: 'Valor não pode ser maior que a meta' })
        return
      }

      if ((operation === 'add' && (goal.currentAmount + numericAmount) > goal.targetAmount)) {
        setErrors({ amount: 'Valor total não pode exceder a meta' })
        return
      }
    }

    onSubmit(numericAmount, operation, description || undefined)
    handleClose()
  }

  const handleClose = () => {
    setAmount('')
    setOperation('add')
    setDescription('')
    setErrors({})
    onClose()
  }

  const formatAmountInput = (value: string) => {
    // Remove non-numeric characters except comma and dot
    const numericValue = value.replace(/[^\d.,]/g, '')

    // Convert to number and format as currency
    const number = parseCurrency(numericValue)
    if (isNaN(number) || number === 0) return ''

    return formatCurrency(number)
  }

  const handleAmountChange = (value: string) => {
    setAmount(value)
    if (errors.amount) {
      setErrors({ ...errors, amount: '' })
    }
  }

  const getPreviewAmount = () => {
    const numericAmount = parseCurrency(amount)
    if (isNaN(numericAmount)) return goal.currentAmount

    if (goal.goalType === 'REDUCTION') {
      const initialAmount = goal.initialAmount || 0
      switch (operation) {
        case 'add':
          return Math.min(goal.currentAmount + numericAmount, initialAmount)
        case 'subtract':
          return Math.max(goal.currentAmount - numericAmount, 0)
        case 'set':
          return Math.min(numericAmount, initialAmount)
        default:
          return goal.currentAmount
      }
    } else {
      switch (operation) {
        case 'add':
          return Math.min(goal.currentAmount + numericAmount, goal.targetAmount)
        case 'subtract':
          return Math.max(goal.currentAmount - numericAmount, 0)
        case 'set':
          return Math.min(numericAmount, goal.targetAmount)
        default:
          return goal.currentAmount
      }
    }
  }

  const getPreviewPercentage = () => {
    const previewAmount = getPreviewAmount()

    if (goal.goalType === 'REDUCTION') {
      const initialAmount = goal.initialAmount || 0
      if (initialAmount > 0) {
        const reducedAmount = initialAmount - previewAmount
        return (reducedAmount / initialAmount) * 100
      }
      return 0
    } else {
      return (previewAmount / goal.targetAmount) * 100
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {goal.goalType === 'REDUCTION' ? (
              <TrendingDown className="h-5 w-5 text-red-500" />
            ) : (
              <TrendingUp className="h-5 w-5 text-green-500" />
            )}
            {goal.goalType === 'REDUCTION' ? 'Atualizar Redução' : 'Atualizar Progresso'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Goal Info */}
          <div className="glass-deep p-4 rounded-lg">
            <h3 className="font-semibold text-foreground mb-2">{goal.name}</h3>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              {goal.goalType === 'REDUCTION' ? (
                <>
                  <span>Atual: {formatCurrency(goal.currentAmount)}</span>
                  <span>Inicial: {formatCurrency(goal.initialAmount || 0)}</span>
                </>
              ) : (
                <>
                  <span>Atual: {formatCurrency(goal.currentAmount)}</span>
                  <span>Meta: {formatCurrency(goal.targetAmount)}</span>
                </>
              )}
            </div>
            <div className="mt-2">
              <div className="w-full bg-secondary-800 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    goal.goalType === 'REDUCTION'
                      ? 'bg-gradient-to-r from-red-500 to-red-400'
                      : 'bg-gradient-deep'
                  }`}
                  style={{
                    width: `${Math.min((() => {
                      if (goal.goalType === 'REDUCTION') {
                        const initialAmount = goal.initialAmount || 0
                        if (initialAmount > 0) {
                          const reducedAmount = initialAmount - goal.currentAmount
                          return (reducedAmount / initialAmount) * 100
                        }
                        return 0
                      } else {
                        return (goal.currentAmount / goal.targetAmount) * 100
                      }
                    })(), 100)}%`
                  }}
                />
              </div>
            </div>
          </div>

          {/* Operation Type */}
          <div className="space-y-2">
            <Label htmlFor="operation">Tipo de Operação</Label>
            <Select value={operation} onValueChange={(value: ProgressOperation) => setOperation(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {goal.goalType === 'REDUCTION' ? (
                  <>
                    <SelectItem value="add">Aumentar dívida</SelectItem>
                    <SelectItem value="subtract">Pagar/Reduzir dívida</SelectItem>
                    <SelectItem value="set">Definir valor atual da dívida</SelectItem>
                  </>
                ) : (
                  <>
                    <SelectItem value="add">Adicionar valor</SelectItem>
                    <SelectItem value="subtract">Subtrair valor</SelectItem>
                    <SelectItem value="set">Definir valor total</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Amount Input */}
          <div className="space-y-2">
            <Label htmlFor="amount">
              {goal.goalType === 'REDUCTION' ? (
                operation === 'add' ? 'Valor a adicionar à dívida' :
                operation === 'subtract' ? 'Valor a pagar/reduzir' :
                'Novo valor atual da dívida'
              ) : (
                operation === 'add' ? 'Valor a adicionar' :
                operation === 'subtract' ? 'Valor a subtrair' :
                'Novo valor total'
              )}
            </Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="amount"
                type="text"
                placeholder="0,00"
                value={amount}
                onChange={(e) => handleAmountChange(e.target.value)}
                onBlur={(e) => {
                  const formatted = formatAmountInput(e.target.value)
                  setAmount(formatted)
                }}
                className={`pl-10 ${errors.amount ? 'border-destructive' : ''}`}
              />
            </div>
            {errors.amount && (
              <p className="text-sm text-destructive">{errors.amount}</p>
            )}
          </div>

          {/* Preview */}
          {amount && !errors.amount && (
            <div className="glass-deep p-4 rounded-lg">
              <h4 className="text-sm font-medium text-foreground mb-2">Preview</h4>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Novo valor:</span>
                <span className="font-semibold text-foreground">
                  {formatCurrency(getPreviewAmount())}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Novo progresso:</span>
                <span className="font-semibold text-foreground">
                  {getPreviewPercentage().toFixed(1)}%
                </span>
              </div>
              <div className="mt-2">
                <div className="w-full bg-secondary-800 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      goal.goalType === 'REDUCTION'
                        ? 'bg-gradient-to-r from-red-500 to-red-400'
                        : 'bg-success'
                    }`}
                    style={{ width: `${Math.min(getPreviewPercentage(), 100)}%` }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Descrição (opcional)</Label>
            <Textarea
              id="description"
              placeholder="Adicione uma descrição para esta atualização..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !amount || !!errors.amount}
              className="bg-gradient-deep text-white"
            >
              {isLoading ? 'Atualizando...' : 'Atualizar Progresso'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
