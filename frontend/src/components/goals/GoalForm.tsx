import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Calendar, DollarSign, Target, Users, X, TrendingUp, TrendingDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'


import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { formatCurrency, parseCurrency, formatDateForInput } from '@/lib/utils'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import type { FinancialGoal, CreateGoalData, UpdateGoalData } from '@/types/goal.types'

// Validation schema
const goalFormSchema = z.object({
  name: z.string()
    .min(1, 'Nome é obrigatório')
    .min(3, 'Nome deve ter pelo menos 3 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres'),
  goalType: z.enum(['ACCUMULATION', 'REDUCTION'])
    .default('ACCUMULATION'),
  targetAmount: z.string()
    .min(1, 'Valor alvo é obrigatório')
    .refine((val) => {
      const amount = parseCurrency(val)
      return amount > 0
    }, 'Valor alvo deve ser maior que zero'),
  currentAmount: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true
      const amount = parseCurrency(val)
      return amount >= 0
    }, 'Valor atual deve ser maior ou igual a zero'),
  initialAmount: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true
      const amount = parseCurrency(val)
      return amount > 0
    }, 'Valor inicial deve ser maior que zero'),
  targetDate: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true
      const date = new Date(val)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      return date >= today
    }, 'Data alvo deve ser hoje ou no futuro'),
  familyMemberIds: z.array(z.string())
    .optional()
    .default([]),
}).refine((data) => {
  // Para metas de redução, valor inicial é obrigatório
  if (data.goalType === 'REDUCTION') {
    return data.initialAmount && parseCurrency(data.initialAmount) > 0
  }
  return true
}, {
  message: 'Para metas de redução, valor inicial é obrigatório',
  path: ['initialAmount']
}).refine((data) => {
  // Para metas de redução, valor atual não pode exceder valor inicial
  if (data.goalType === 'REDUCTION' && data.initialAmount && data.currentAmount) {
    const initial = parseCurrency(data.initialAmount)
    const current = parseCurrency(data.currentAmount)
    return current <= initial
  }
  return true
}, {
  message: 'Para metas de redução, valor atual não pode ser maior que o valor inicial',
  path: ['currentAmount']
}).refine((data) => {
  // Para metas de acumulação, valor atual não pode exceder valor alvo
  if (data.goalType === 'ACCUMULATION' && data.targetAmount && data.currentAmount) {
    const target = parseCurrency(data.targetAmount)
    const current = parseCurrency(data.currentAmount)
    return current <= target
  }
  return true
}, {
  message: 'Para metas de acumulação, valor atual não pode ser maior que o valor alvo',
  path: ['currentAmount']
})

type GoalFormData = z.infer<typeof goalFormSchema>

interface GoalFormProps {
  mode: 'create' | 'edit'
  goal?: FinancialGoal | null
  onSubmit: (data: CreateGoalData | UpdateGoalData) => void
  onCancel: () => void
  isLoading?: boolean
  submitText?: string
}

export function GoalForm({
  mode,
  goal,
  onSubmit,
  onCancel,
  isLoading = false,
  submitText = 'Salvar'
}: GoalFormProps) {
  const { data: familyMembersData, isLoading: isLoadingMembers } = useFamilyMembers()
  const familyMembers = familyMembersData?.data || []

  const form = useForm<GoalFormData>({
    resolver: zodResolver(goalFormSchema),
    defaultValues: {
      name: '',
      goalType: 'ACCUMULATION',
      targetAmount: '',
      currentAmount: '',
      initialAmount: '',
      targetDate: '',
      familyMemberIds: [],
    },
  })

  // Load goal data for editing
  useEffect(() => {
    if (mode === 'edit' && goal) {
      form.reset({
        name: goal.name,
        goalType: goal.goalType || 'ACCUMULATION',
        targetAmount: formatCurrency(goal.targetAmount),
        currentAmount: formatCurrency(goal.currentAmount),
        initialAmount: goal.initialAmount ? formatCurrency(goal.initialAmount) : '',
        targetDate: goal.targetDate ? formatDateForInput(goal.targetDate) : '',
        familyMemberIds: goal.members?.map(m => m.id) || [],
      })
    }
  }, [mode, goal, form])

  const handleSubmit = (data: GoalFormData) => {
    const targetAmount = data.goalType === 'REDUCTION' ? 0 : parseCurrency(data.targetAmount)
    const currentAmount = data.currentAmount ? parseCurrency(data.currentAmount) : 0
    const initialAmount = data.initialAmount ? parseCurrency(data.initialAmount) : undefined

    const formData = {
      name: data.name.trim(),
      goalType: data.goalType,
      targetAmount,
      currentAmount,
      initialAmount,
      targetDate: data.targetDate ? new Date(data.targetDate + 'T00:00:00.000Z').toISOString() : undefined,
      familyMemberIds: data.familyMemberIds || []
    }

    onSubmit(formData)
  }

  const formatAmountInput = (value: string) => {
    // Remove non-numeric characters except comma and dot
    const numericValue = value.replace(/[^\d.,]/g, '')
    
    // Convert to number and format as currency
    const number = parseCurrency(numericValue)
    if (isNaN(number) || number === 0) return ''
    
    return formatCurrency(number)
  }

  const handleAmountChange = (field: any, value: string) => {
    field.onChange(value)
  }

  const handleAmountBlur = (field: any, value: string) => {
    const formatted = formatAmountInput(value)
    field.onChange(formatted)
  }



  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Goal Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Nome da Meta
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Ex: Comprar um carro novo"
                  {...field}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Goal Type */}
        <FormField
          control={form.control}
          name="goalType"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Tipo de Meta
              </FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  value={field.value}
                  className="grid grid-cols-2 gap-4"
                  disabled={isLoading}
                >
                  <div className="flex items-center space-x-2 glass-deep p-4 rounded-lg cursor-pointer hover:bg-secondary/50 transition-colors">
                    <RadioGroupItem value="ACCUMULATION" id="accumulation" />
                    <Label htmlFor="accumulation" className="flex items-center gap-2 cursor-pointer flex-1">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      <div>
                        <div className="font-medium">Acumulação</div>
                        <div className="text-xs text-muted-foreground">Juntar dinheiro para atingir um valor</div>
                      </div>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 glass-deep p-4 rounded-lg cursor-pointer hover:bg-secondary/50 transition-colors">
                    <RadioGroupItem value="REDUCTION" id="reduction" />
                    <Label htmlFor="reduction" className="flex items-center gap-2 cursor-pointer flex-1">
                      <TrendingDown className="h-4 w-4 text-red-500" />
                      <div>
                        <div className="font-medium">Redução</div>
                        <div className="text-xs text-muted-foreground">Quitar dívidas ou reduzir saldo</div>
                      </div>
                    </Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Target Amount - Only for ACCUMULATION */}
        {form.watch('goalType') === 'ACCUMULATION' && (
          <FormField
            control={form.control}
            name="targetAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Valor Alvo
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="0,00"
                      {...field}
                      onChange={(e) => handleAmountChange(field, e.target.value)}
                      onBlur={(e) => handleAmountBlur(field, e.target.value)}
                      className="pl-10"
                      disabled={isLoading}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Initial Amount - Only for REDUCTION */}
        {form.watch('goalType') === 'REDUCTION' && (
          <FormField
            control={form.control}
            name="initialAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Valor Inicial da Dívida
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="0,00"
                      {...field}
                      onChange={(e) => handleAmountChange(field, e.target.value)}
                      onBlur={(e) => handleAmountBlur(field, e.target.value)}
                      className="pl-10"
                      disabled={isLoading}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Current Amount */}
        <FormField
          control={form.control}
          name="currentAmount"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                {form.watch('goalType') === 'REDUCTION' ? 'Valor Atual da Dívida' : 'Valor Atual (opcional)'}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="0,00"
                    {...field}
                    onChange={(e) => handleAmountChange(field, e.target.value)}
                    onBlur={(e) => handleAmountBlur(field, e.target.value)}
                    className="pl-10"
                    disabled={isLoading}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Target Date */}
        <FormField
          control={form.control}
          name="targetDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Data Alvo (opcional)
              </FormLabel>
              <FormControl>
                <Input
                  type="date"
                  {...field}
                  disabled={isLoading}
                  min={new Date().toISOString().split('T')[0]}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Family Members */}
        <FormField
          control={form.control}
          name="familyMemberIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Membros da Família (opcional)
              </FormLabel>
              <FormControl>
                <div className="space-y-3">
                  {/* Selected Members */}
                  {field.value && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {field.value.map((memberId) => {
                        const member = familyMembers.find(m => m.id === memberId)
                        if (!member) return null

                        return (
                          <Badge
                            key={memberId}
                            variant="outline"
                            className="flex items-center gap-2 pr-1"
                            style={{
                              borderColor: member.color,
                              color: member.color
                            }}
                          >
                            {member.color && (
                              <div
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: member.color }}
                              />
                            )}
                            {member.name}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                              onClick={() => {
                                const newValue = (field.value || []).filter(id => id !== memberId)
                                field.onChange(newValue)
                              }}
                              disabled={isLoading}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        )
                      })}
                    </div>
                  )}

                  {/* Add Member Selector */}
                  <Select
                    onValueChange={(memberId) => {
                      if (memberId && field.value && !field.value.includes(memberId)) {
                        field.onChange([...field.value, memberId])
                      }
                    }}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um membro da família" />
                    </SelectTrigger>
                    <SelectContent>
                      {familyMembers && familyMembers.length > 0 ? (
                        familyMembers
                          .filter(member => !field.value || !field.value.includes(member.id))
                          .map((member) => (
                          <SelectItem key={member.id} value={member.id}>
                            <div className="flex items-center gap-2">
                              {member.color && (
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: member.color }}
                                />
                              )}
                              {member.name}
                            </div>
                          </SelectItem>
                          ))
                      ) : null}
                      {(!familyMembers || familyMembers.length === 0 || familyMembers.filter(member => !field.value || !field.value.includes(member.id)).length === 0) && (
                        <div className="px-2 py-1.5 text-sm text-muted-foreground">
                          {(!familyMembers || familyMembers.length === 0)
                            ? 'Nenhum membro cadastrado'
                            : 'Todos os membros já foram selecionados'
                          }
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Progress Preview */}
        {((form.watch('goalType') === 'ACCUMULATION' && form.watch('targetAmount') && form.watch('currentAmount')) ||
          (form.watch('goalType') === 'REDUCTION' && form.watch('initialAmount') && form.watch('currentAmount'))) && (
          <div className="glass-deep p-4 rounded-lg">
            <h4 className="text-sm font-medium text-foreground mb-2">Preview do Progresso</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  {form.watch('goalType') === 'REDUCTION' ? 'Progresso de redução:' : 'Progresso atual:'}
                </span>
                <span className="font-semibold text-foreground">
                  {(() => {
                    const goalType = form.watch('goalType')
                    const current = parseCurrency(form.watch('currentAmount') || '0')

                    if (goalType === 'REDUCTION') {
                      const initial = parseCurrency(form.watch('initialAmount') || '0')
                      if (initial > 0) {
                        const reduced = initial - current
                        const percentage = (reduced / initial) * 100
                        return `${percentage.toFixed(1)}%`
                      }
                      return '0.0%'
                    } else {
                      const target = parseCurrency(form.watch('targetAmount') || '0')
                      const percentage = target > 0 ? (current / target) * 100 : 0
                      return `${percentage.toFixed(1)}%`
                    }
                  })()}
                </span>
              </div>
              <div className="w-full bg-secondary-800 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    form.watch('goalType') === 'REDUCTION' ? 'bg-gradient-to-r from-red-500 to-red-400' : 'bg-gradient-deep'
                  }`}
                  style={{
                    width: `${Math.min((() => {
                      const goalType = form.watch('goalType')
                      const current = parseCurrency(form.watch('currentAmount') || '0')

                      if (goalType === 'REDUCTION') {
                        const initial = parseCurrency(form.watch('initialAmount') || '0')
                        if (initial > 0) {
                          const reduced = initial - current
                          return (reduced / initial) * 100
                        }
                        return 0
                      } else {
                        const target = parseCurrency(form.watch('targetAmount') || '0')
                        return target > 0 ? (current / target) * 100 : 0
                      }
                    })(), 100)}%`
                  }}
                />
              </div>
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>
                  {form.watch('goalType') === 'REDUCTION' ? 'Valor restante:' : 'Falta:'}
                </span>
                <span>
                  {(() => {
                    const goalType = form.watch('goalType')
                    const current = parseCurrency(form.watch('currentAmount') || '0')

                    if (goalType === 'REDUCTION') {
                      return formatCurrency(current)
                    } else {
                      const target = parseCurrency(form.watch('targetAmount') || '0')
                      const remaining = Math.max(0, target - current)
                      return formatCurrency(remaining)
                    }
                  })()}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-gradient-deep text-white"
          >
            {isLoading ? 'Salvando...' : submitText}
          </Button>
        </div>
      </form>
    </Form>
  )
}
