import {
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  DollarSign,
  Activity,
  BarChart3,
  Calculator,
  Clock,
  Calendar,
  AlertCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import type { TransactionStats, TransactionSummary } from '@/types/transaction.types'

interface TransactionsStatsCardsProps {
  stats?: TransactionStats
  summary?: TransactionSummary
  isLoading?: boolean
  currency?: string
}

export function TransactionsStatsCards({
  stats,
  summary,
  isLoading = false,
  currency = 'BRL'
}: TransactionsStatsCardsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index} className="glass-deep shadow-elegant">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold">
                <div className="h-4 bg-muted animate-pulse rounded w-24" />
              </CardTitle>
              <div className="h-10 w-10 bg-muted animate-pulse rounded-xl" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted animate-pulse rounded w-32 mb-2" />
              <div className="h-4 bg-muted animate-pulse rounded w-20" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const getGrowthBadge = (percentage: number) => {
    if (percentage > 0) {
      return (
        <Badge variant="secondary" className="text-green-600 bg-green-50">
          <TrendingUp className="h-3 w-3 mr-1" />
          +{percentage.toFixed(1)}%
        </Badge>
      )
    } else if (percentage < 0) {
      return (
        <Badge variant="secondary" className="text-red-600 bg-red-50">
          <TrendingDown className="h-3 w-3 mr-1" />
          {percentage.toFixed(1)}%
        </Badge>
      )
    } else {
      return (
        <Badge variant="secondary" className="text-muted-foreground">
          <Activity className="h-3 w-3 mr-1" />
          0%
        </Badge>
      )
    }
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Transactions */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">
            Total de Transações
          </CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
            <BarChart3 className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-foreground">
            {stats?.totalTransactions || 0}
          </div>
          {stats?.growthPercentage !== undefined && (
            <div className="flex items-center gap-2 mt-2">
              {getGrowthBadge(stats.growthPercentage)}
              <p className="text-sm text-muted-foreground">
                vs. mês anterior
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Total Income */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">
            Total de Receitas
          </CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-green-500 shadow-soft">
            <TrendingUp className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-green-600">
            {formatCurrency(stats?.totalIncome || summary?.totalIncome || 0, currency)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Entradas no período
          </p>
        </CardContent>
      </Card>

      {/* Total Expenses */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">
            Total de Despesas
          </CardTitle>
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-red-500 shadow-soft">
            <TrendingDown className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-red-600">
            {formatCurrency(Math.abs(stats?.totalExpenses || summary?.totalExpenses || 0), currency)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Saídas no período
          </p>
        </CardContent>
      </Card>

      {/* Net Amount */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">
            Saldo Líquido
          </CardTitle>
          <div className={`flex h-10 w-10 items-center justify-center rounded-xl shadow-soft ${
            (stats?.netAmount || summary?.netAmount || 0) >= 0 
              ? 'bg-blue-500' 
              : 'bg-orange-500'
          }`}>
            {(stats?.netAmount || summary?.netAmount || 0) >= 0 ? (
              <DollarSign className="h-5 w-5 text-white" />
            ) : (
              <Calculator className="h-5 w-5 text-white" />
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className={`text-3xl font-bold ${
            (stats?.netAmount || summary?.netAmount || 0) >= 0 
              ? 'text-blue-600' 
              : 'text-orange-600'
          }`}>
            {formatCurrency(stats?.netAmount || summary?.netAmount || 0, currency)}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Receitas - Despesas
          </p>
        </CardContent>
      </Card>

      {/* Additional Stats if available */}
      {stats?.averageTransaction !== undefined && (
        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Valor Médio
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-purple-500 shadow-soft">
              <Calculator className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">
              {formatCurrency(stats.averageTransaction, currency)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Por transação
            </p>
          </CardContent>
        </Card>
      )}

      {summary?.totalTransfers !== undefined && summary.totalTransfers !== 0 && (
        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Total de Transferências
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-indigo-500 shadow-soft">
              <ArrowRightLeft className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-indigo-600">
              {formatCurrency(Math.abs(summary.totalTransfers), currency)}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Entre contas
            </p>
          </CardContent>
        </Card>
      )}

      {stats?.thisMonthTransactions !== undefined && (
        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Este Mês
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-teal-500 shadow-soft">
              <Activity className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-teal-600">
              {stats.thisMonthTransactions}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Transações em {new Date().toLocaleDateString('pt-BR', { month: 'long' })}
            </p>
          </CardContent>
        </Card>
      )}

      {stats?.lastMonthTransactions !== undefined && (
        <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-foreground">
              Mês Anterior
            </CardTitle>
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-500 shadow-soft">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-600">
              {stats.lastMonthTransactions}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Para comparação
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
