import React, { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
// import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Switch } from '../ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Plus,
  Minus,
  // Calendar,
  DollarSign,
  CreditCard,
  Calculator,
  AlertTriangle,
  ArrowRightLeft,
  Tag,
  Users
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import {
  CreateTransactionData,
  UpdateTransactionData,
  TransactionType,
  TRANSACTION_TYPES,
  TRANSACTION_TYPE_LABELS,
  RecurrenceFrequency,
  RECURRENCE_FREQUENCIES,
  RECURRENCE_FREQUENCY_LABELS,
  RecurringTransactionData
  // InstallmentFormData
} from '@/types/transaction.types'
import { TagSelector } from './TagSelector'

// Form validation schema
const installmentSchema = z.object({
  amount: z.number().min(0.01, 'Valor deve ser maior que zero'),
  dueDate: z.string().min(1, 'Data é obrigatória'),
  isPaid: z.boolean().default(false),
  description: z.string().optional()
})

const transactionFormSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  totalAmount: z.number().min(0.01, 'Valor total deve ser maior que zero'),
  transactionDate: z.string().min(1, 'Data é obrigatória'),
  type: z.nativeEnum(TRANSACTION_TYPES),
  accountId: z.string().min(1, 'Conta é obrigatória'),
  categoryId: z.string().optional(),
  destinationAccountId: z.string().optional(),
  exchangeRate: z.number().positive().optional(),
  isFuture: z.boolean().default(false),
  installments: z.array(installmentSchema).min(1, 'Pelo menos 1 parcela é obrigatória'),
  tagIds: z.array(z.string()).default([]),
  familyMemberId: z.string().optional(), // Changed from familyMemberIds array to single familyMemberId
  // Recurring transaction fields
  isRecurring: z.boolean().default(false),
  recurrenceFrequency: z.nativeEnum(RECURRENCE_FREQUENCIES).optional(),
  recurrenceEndDate: z.string().optional()
}).superRefine((data, ctx) => {
  // For transfers, destination account is required and must be different from source
  if (data.type === TRANSACTION_TYPES.TRANSFER) {
    if (!data.destinationAccountId) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Conta de destino é obrigatória para transferências",
        path: ["destinationAccountId"]
      })
    }
    if (data.destinationAccountId === data.accountId) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Conta de destino deve ser diferente da conta de origem",
        path: ["destinationAccountId"]
      })
    }
  }

  // For recurring transactions, frequency and end date are required
  if (data.isRecurring) {
    if (!data.recurrenceFrequency) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Frequência é obrigatória para transações recorrentes",
        path: ["recurrenceFrequency"]
      })
    }
    if (!data.recurrenceEndDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Data final é obrigatória para transações recorrentes",
        path: ["recurrenceEndDate"]
      })
    }
  }
})

type TransactionFormData = z.infer<typeof transactionFormSchema>

interface TransactionFormProps {
  initialData?: Partial<CreateTransactionData | UpdateTransactionData>
  onSubmit: (data: CreateTransactionData | UpdateTransactionData) => void
  onCancel: () => void
  isLoading?: boolean
  accounts: Array<{ id: string; name: string; type: string; currency: string }>
  categories: Array<{ id: string; name: string; color: string }>
  familyMembers: Array<{ id: string; name: string; color: string }>
  tags: Array<{ id: string; name: string; color: string }>
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  accounts,
  categories,
  familyMembers,
  tags
}) => {
  // Ensure arrays are valid
  const safeAccounts = Array.isArray(accounts) ? accounts : []
  const safeCategories = Array.isArray(categories) ? categories : []
  const safeFamilyMembers = Array.isArray(familyMembers) ? familyMembers : []
  const safeTags = Array.isArray(tags) ? tags : []

  // Debug log
  console.log('TransactionForm - safeAccounts:', safeAccounts)

  const [installmentMode, setInstallmentMode] = useState<'single' | 'multiple' | 'recurring'>('single')
  const [autoCalculate, setAutoCalculate] = useState(true)

  const form = useForm<TransactionFormData>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      description: initialData?.description || '',
      totalAmount: initialData?.totalAmount || 1, // Changed from 0 to 1 to pass validation
      transactionDate: initialData?.transactionDate
        ? new Date(initialData.transactionDate).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0],
      type: (initialData as any)?.type || TRANSACTION_TYPES.EXPENSE,
      accountId: (initialData as any)?.accountId || (safeAccounts.length > 0 ? safeAccounts[0].id : ''),
      categoryId: initialData?.categoryId || '',
      destinationAccountId: initialData?.destinationAccountId || '',
      isFuture: initialData?.isFuture || false,
      installments: (initialData as any)?.installments || [{
        amount: initialData?.totalAmount || 1, // Changed from 0 to 1
        dueDate: initialData?.transactionDate
          ? new Date(initialData.transactionDate).toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
        isPaid: false,
        description: ''
      }],
      tagIds: initialData?.tagIds || [],
      familyMemberId: (initialData as any)?.familyMemberId || (initialData as any)?.familyMemberIds?.[0] || '',
      // Recurring transaction fields
      isRecurring: false,
      recurrenceFrequency: undefined,
      recurrenceEndDate: ''
    }
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'installments'
  })

  const watchedValues = form.watch()
  const totalAmount = watchedValues.totalAmount
  const installments = watchedValues.installments
  const transactionType = watchedValues.type
  const selectedAccountId = watchedValues.accountId

  // Filter available destination accounts (exclude selected source account)
  const availableDestinationAccounts = safeAccounts.filter(
    account => account.id !== selectedAccountId
  )

  // Auto-select first account if none is selected and accounts are available
  useEffect(() => {
    if (safeAccounts.length > 0 && !watchedValues.accountId) {
      form.setValue('accountId', safeAccounts[0].id)
    }
  }, [safeAccounts, watchedValues.accountId, form])

  // Auto-calculate installment amounts when total changes
  useEffect(() => {
    if (autoCalculate && installments.length > 0 && totalAmount > 0) {
      const amountPerInstallment = totalAmount / installments.length
      const updatedInstallments = installments.map((inst, index) => ({
        ...inst,
        amount: index === installments.length - 1
          ? totalAmount - (amountPerInstallment * (installments.length - 1)) // Last installment gets remainder
          : amountPerInstallment
      }))

      form.setValue('installments', updatedInstallments)
    }
  }, [totalAmount, installments.length, autoCalculate, form])

  const handleInstallmentModeChange = (mode: 'single' | 'multiple' | 'recurring') => {
    setInstallmentMode(mode)

    if (mode === 'single') {
      form.setValue('isRecurring', false)
      form.setValue('installments', [{
        amount: totalAmount,
        dueDate: watchedValues.transactionDate,
        isPaid: false,
        description: ''
      }])
    } else if (mode === 'multiple') {
      form.setValue('isRecurring', false)
      // Add a second installment for multiple mode
      if (installments.length === 1) {
        append({
          amount: totalAmount / 2,
          dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // +30 days
          isPaid: false,
          description: ''
        })

        // Update first installment amount
        form.setValue('installments.0.amount', totalAmount / 2)
      }
    } else if (mode === 'recurring') {
      form.setValue('isRecurring', true)
      form.setValue('installments', [{
        amount: totalAmount,
        dueDate: watchedValues.transactionDate,
        isPaid: false,
        description: ''
      }])
      // Set default end date to 1 year from now
      const nextYear = new Date()
      nextYear.setFullYear(nextYear.getFullYear() + 1)
      form.setValue('recurrenceEndDate', nextYear.toISOString().split('T')[0])
      form.setValue('recurrenceFrequency', RECURRENCE_FREQUENCIES.MONTHLY)
    }
  }

  const addInstallment = () => {
    const lastInstallment = installments[installments.length - 1]
    const nextDate = new Date(lastInstallment.dueDate)
    nextDate.setMonth(nextDate.getMonth() + 1) // Add 1 month
    
    append({
      amount: 0,
      dueDate: nextDate.toISOString().split('T')[0],
      isPaid: false,
      description: ''
    })
  }

  const removeInstallment = (index: number) => {
    if (installments.length > 1) {
      remove(index)
    }
  }

  const redistributeAmounts = () => {
    if (installments.length > 0 && totalAmount > 0) {
      const amountPerInstallment = totalAmount / installments.length
      const updatedInstallments = installments.map((inst, index) => ({
        ...inst,
        amount: index === installments.length - 1 
          ? totalAmount - (amountPerInstallment * (installments.length - 1))
          : amountPerInstallment
      }))
      
      form.setValue('installments', updatedInstallments)
    }
  }

  const calculateInstallmentsSum = () => {
    return installments.reduce((sum, inst) => sum + (inst.amount || 0), 0)
  }

  const isValidSum = () => {
    // Allow submission if total amount is less than 0.01 (user will fill it)
    if (totalAmount < 0.01) return true

    const sum = calculateInstallmentsSum()
    return Math.abs(sum - totalAmount) < 0.01
  }

  const handleSubmit = (data: any) => {
    console.log('TransactionForm - handleSubmit data:', data)
    console.log('TransactionForm - isRecurring:', data.isRecurring)
    console.log('TransactionForm - installmentMode:', installmentMode)

    // Ensure accountId is set
    if (!data.accountId && safeAccounts.length > 0) {
      data.accountId = safeAccounts[0].id
      form.setValue('accountId', safeAccounts[0].id)
    }

    // Force isRecurring based on installmentMode if not set correctly
    if (installmentMode === 'recurring') {
      data.isRecurring = true
    }

    if (data.isRecurring || installmentMode === 'recurring') {
      // Handle recurring transaction
      const recurringData: RecurringTransactionData = {
        description: data.description,
        fixedAmount: data.totalAmount,
        frequency: data.recurrenceFrequency!,
        startDate: new Date(data.transactionDate + 'T00:00:00.000Z').toISOString(),
        endDate: data.recurrenceEndDate ? new Date(data.recurrenceEndDate + 'T00:00:00.000Z').toISOString() : undefined,
        type: data.type,
        accountId: data.accountId,
        categoryId: data.categoryId || undefined,
        tagIds: data.tagIds && data.tagIds.length > 0 ? data.tagIds : undefined,
        familyMemberId: data.familyMemberId || undefined
      }

      // Clean up empty/unnecessary fields
      if (!recurringData.categoryId || recurringData.categoryId === '') {
        delete recurringData.categoryId
      }

      if (!recurringData.tagIds || recurringData.tagIds.length === 0) {
        delete recurringData.tagIds
      }

      if (!recurringData.familyMemberId || recurringData.familyMemberId === '') {
        delete recurringData.familyMemberId
      }

      console.log('Submitting recurring transaction data:', recurringData)
      // Ensure isRecurring flag is set
      const recurringDataWithFlag = { ...recurringData, isRecurring: true }
      console.log('Final recurring data with flag:', recurringDataWithFlag)
      onSubmit(recurringDataWithFlag as any)
    } else {
      // Handle regular transaction
      const formattedData: any = {
        ...data,
        transactionDate: new Date(data.transactionDate + 'T00:00:00.000Z').toISOString(),
        installments: data.installments.map((installment: any) => ({
          ...installment,
          dueDate: new Date(installment.dueDate + 'T00:00:00.000Z').toISOString()
        }))
      }

      // Clean up empty/unnecessary fields
      if (!formattedData.destinationAccountId || formattedData.destinationAccountId === '') {
        delete formattedData.destinationAccountId
      }

      if (!formattedData.categoryId || formattedData.categoryId === '') {
        delete formattedData.categoryId
      }

      if (!formattedData.exchangeRate) {
        delete formattedData.exchangeRate
      }

      // Remove empty arrays
      if (formattedData.tagIds && formattedData.tagIds.length === 0) {
        delete formattedData.tagIds
      }

      // Convert single familyMemberId to array for backend compatibility
      if (formattedData.familyMemberId) {
        formattedData.familyMemberIds = [formattedData.familyMemberId]
        delete formattedData.familyMemberId
      }

      console.log('Submitting transaction data:', formattedData)
      onSubmit(formattedData)
    }
  }

  const handleFormError = (errors: any) => {
    console.error('Form validation errors:', errors)

    // Show specific error messages for debugging
    if (errors.accountId) {
      console.error('AccountId error:', errors.accountId)
    }
    if (errors.recurrenceFrequency) {
      console.error('RecurrenceFrequency error:', errors.recurrenceFrequency)
    }
    if (errors.recurrenceEndDate) {
      console.error('RecurrenceEndDate error:', errors.recurrenceEndDate)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(handleSubmit, handleFormError)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Informações Básicas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Input
                id="description"
                {...form.register('description')}
                placeholder="Ex: Compra no supermercado"
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo</Label>
              <Select
                value={watchedValues.type}
                onValueChange={(value) => form.setValue('type', value as TransactionType)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(TRANSACTION_TYPE_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="totalAmount">Valor Total</Label>
              <Input
                id="totalAmount"
                type="number"
                step="0.01"
                {...form.register('totalAmount', { valueAsNumber: true })}
                placeholder="0.00"
              />
              {form.formState.errors.totalAmount && (
                <p className="text-sm text-red-500">{form.formState.errors.totalAmount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="transactionDate">Data</Label>
              <Input
                id="transactionDate"
                type="date"
                {...form.register('transactionDate')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="accountId">
                {transactionType === TRANSACTION_TYPES.TRANSFER ? 'Conta de Origem' : 'Conta'}
              </Label>
              <Select
                value={watchedValues.accountId}
                onValueChange={(value) => form.setValue('accountId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma conta" />
                </SelectTrigger>
                <SelectContent>
                  {safeAccounts.length === 0 ? (
                    <SelectItem value="" disabled>Nenhuma conta disponível</SelectItem>
                  ) : (
                    safeAccounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.name} ({account.type})
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {form.formState.errors.accountId && (
                <p className="text-sm text-red-500">{form.formState.errors.accountId.message}</p>
              )}
            </div>
          </div>

          {/* Transfer Destination Account */}
          {transactionType === TRANSACTION_TYPES.TRANSFER && (
            <div className="space-y-2">
              <Label htmlFor="destinationAccountId" className="flex items-center gap-2">
                <ArrowRightLeft className="h-4 w-4" />
                Conta de Destino
              </Label>
              <Select
                value={watchedValues.destinationAccountId}
                onValueChange={(value) => form.setValue('destinationAccountId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a conta de destino" />
                </SelectTrigger>
                <SelectContent>
                  {availableDestinationAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name} ({account.type}) - {account.currency}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.destinationAccountId && (
                <p className="text-sm text-red-500">{form.formState.errors.destinationAccountId.message}</p>
              )}
            </div>
          )}

          {transactionType !== TRANSACTION_TYPES.TRANSFER && (
            <div className="space-y-2">
              <Label htmlFor="categoryId">Categoria</Label>
              <Select
                value={watchedValues.categoryId}
                onValueChange={(value) => form.setValue('categoryId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {safeCategories.map((category) => (
                    <SelectItem
                      key={category.id}
                      value={category.id}
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        {category.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Family Member Selection */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Membro da Família
            </Label>
            <Select
              value={watchedValues.familyMemberId || undefined}
              onValueChange={(value) => form.setValue('familyMemberId', value || '')}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecionar membro da família (opcional)" />
              </SelectTrigger>
              <SelectContent>
                {safeFamilyMembers.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: member.color }}
                      />
                      {member.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {watchedValues.familyMemberId && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => form.setValue('familyMemberId', '')}
                className="text-muted-foreground hover:text-foreground"
              >
                Limpar seleção
              </Button>
            )}
          </div>

          {/* Tags Selection */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Tags
            </Label>
            <TagSelector
              tags={safeTags}
              selectedTagIds={watchedValues.tagIds || []}
              onSelectionChange={(tagIds) => form.setValue('tagIds', tagIds)}
              placeholder="Selecionar tags..."
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Installments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Parcelas
            </div>
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant={installmentMode === 'single' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleInstallmentModeChange('single')}
              >
                À Vista
              </Button>
              <Button
                type="button"
                variant={installmentMode === 'multiple' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleInstallmentModeChange('multiple')}
              >
                Parcelado
              </Button>
              <Button
                type="button"
                variant={installmentMode === 'recurring' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleInstallmentModeChange('recurring')}
              >
                Recorrente
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Recurring transaction fields */}
          {installmentMode === 'recurring' && (
            <div className="space-y-4 p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
              <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                <Calculator className="h-4 w-4" />
                <span className="font-medium">Configuração de Recorrência</span>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="recurrenceFrequency">Frequência</Label>
                  <Select
                    value={watchedValues.recurrenceFrequency}
                    onValueChange={(value) => form.setValue('recurrenceFrequency', value as RecurrenceFrequency)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a frequência" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(RECURRENCE_FREQUENCY_LABELS).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.recurrenceFrequency && (
                    <p className="text-sm text-red-500">{form.formState.errors.recurrenceFrequency.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recurrenceEndDate">Repetir até</Label>
                  <Input
                    id="recurrenceEndDate"
                    type="date"
                    {...form.register('recurrenceEndDate')}
                  />
                  {form.formState.errors.recurrenceEndDate && (
                    <p className="text-sm text-red-500">{form.formState.errors.recurrenceEndDate.message}</p>
                  )}
                </div>
              </div>

              <div className="text-sm text-blue-600 dark:text-blue-400">
                <p>💡 As transações serão criadas automaticamente conforme a frequência selecionada.</p>
                <p>Transações com data passada serão marcadas como "pagas", futuras como "pendentes".</p>
              </div>
            </div>
          )}

          {/* Auto-calculate toggle - only show for non-recurring */}
          {installmentMode !== 'recurring' && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Switch
                  checked={autoCalculate}
                  onCheckedChange={setAutoCalculate}
                />
                <Label>Calcular valores automaticamente</Label>
              </div>
              {!autoCalculate && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={redistributeAmounts}
                >
                  Redistribuir Valores
                </Button>
              )}
            </div>
          )}

          {/* Installments list - only show for non-recurring */}
          {installmentMode !== 'recurring' && (
            <div className="space-y-3">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="flex-1 grid grid-cols-3 gap-3">
                    <div className="space-y-1">
                      <Label className="text-xs">Valor</Label>
                      <Input
                        type="number"
                        step="0.01"
                        {...form.register(`installments.${index}.amount`, { valueAsNumber: true })}
                        disabled={autoCalculate}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">Data de Vencimento</Label>
                      <Input
                        type="date"
                        {...form.register(`installments.${index}.dueDate`)}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">Descrição (opcional)</Label>
                      <Input
                        {...form.register(`installments.${index}.description`)}
                        placeholder={`Parcela ${index + 1}/${installments.length}`}
                      />
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Badge variant="outline">
                      {index + 1}/{installments.length}
                    </Badge>
                    {installments.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeInstallment(index)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Add installment button */}
          {installmentMode === 'multiple' && (
            <Button
              type="button"
              variant="outline"
              onClick={addInstallment}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Parcela
            </Button>
          )}

          {/* Sum validation - only show for non-recurring */}
          {installmentMode !== 'recurring' && (
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center gap-2">
                {isValidSum() ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <DollarSign className="h-4 w-4" />
                    <span className="text-sm font-medium">Valores corretos</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-red-600">
                    <AlertTriangle className="h-4 w-4" />
                    <span className="text-sm font-medium">Soma incorreta</span>
                  </div>
                )}
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">Soma: </span>
                <span className={isValidSum() ? 'text-green-600' : 'text-red-600'}>
                  {formatCurrency(calculateInstallmentsSum())}
                </span>
                <span className="text-muted-foreground"> / {formatCurrency(totalAmount)}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isLoading || (installmentMode !== 'recurring' && !isValidSum())}
        >
          {isLoading ? 'Salvando...' : (installmentMode === 'recurring' ? 'Criar Transações Recorrentes' : 'Salvar Transação')}
        </Button>
      </div>
    </form>
  )
}
