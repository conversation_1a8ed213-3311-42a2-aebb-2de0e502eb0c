// Financial Goals Types
// Based on backend documentation and Prisma schema

import { BaseEntity } from './global'

// Goal Type Enum
export type GoalType = 'ACCUMULATION' | 'REDUCTION'

// Goal Status Types
export type GoalStatus = 'not_started' | 'in_progress' | 'completed' | 'overdue'

// Progress Operation Types
export type ProgressOperation = 'add' | 'subtract' | 'set'

// Base Financial Goal Interface
export interface FinancialGoal extends BaseEntity {
  name: string
  goalType: GoalType
  targetAmount: number
  currentAmount: number
  initialAmount?: number
  targetDate?: string
  deletedAt?: string
  version: number

  // Relationships
  members?: GoalMember[]
  milestones?: GoalMilestone[]

  // Computed fields (from backend)
  progress?: GoalProgress
}

// Goal Member Association
export interface GoalMember {
  id: string
  goalId: string
  familyMemberId: string
  createdAt: string
  
  // Populated family member data
  familyMember?: {
    id: string
    name: string
    color?: string
    avatar?: string
  }
}

// Goal Milestone Interface
export interface GoalMilestone extends BaseEntity {
  goalId: string
  name: string
  targetAmount: number
  targetDate: string
  isCompleted: boolean
  version: number
  
  // Relationship
  goal?: FinancialGoal
}

// Goal Progress Interface
export interface GoalProgress {
  percentage: number
  remainingAmount: number
  isCompleted: boolean
  isOverdue: boolean
  daysRemaining?: number
  status: GoalStatus
}

// Create Goal Data
export interface CreateGoalData {
  name: string
  goalType: GoalType
  targetAmount: number
  currentAmount?: number
  initialAmount?: number
  targetDate?: string | Date
  familyMemberIds?: string[]
}

// Update Goal Data
export interface UpdateGoalData {
  name?: string
  goalType?: GoalType
  targetAmount?: number
  currentAmount?: number
  initialAmount?: number
  targetDate?: string | Date
  familyMemberIds?: string[]
}

// Update Progress Data
export interface UpdateProgressData {
  amount: number
  operation: ProgressOperation
  description?: string
}

// Create Milestone Data
export interface CreateMilestoneData {
  name: string
  targetAmount: number
  targetDate: string | Date
}

// Update Milestone Data
export interface UpdateMilestoneData {
  name?: string
  targetAmount?: number
  targetDate?: string | Date
  isCompleted?: boolean
}

// Goal Filters Interface
export interface GoalFilters {
  familyMemberId?: string
  status?: 'active' | 'completed' | 'overdue' | 'all'
  minTargetAmount?: number
  maxTargetAmount?: number
  targetDateFrom?: string
  targetDateTo?: string
  page?: number
  limit?: number
  sortBy?: 'createdAt' | 'targetDate' | 'targetAmount' | 'name'
  sortOrder?: 'asc' | 'desc'
  includeCompleted?: boolean
  includeMilestones?: boolean
  search?: string
}

// Milestone Filters Interface
export interface MilestoneFilters {
  goalId: string
  status?: 'pending' | 'completed' | 'all'
  targetDateFrom?: string
  targetDateTo?: string
  sortBy?: 'targetDate' | 'name' | 'targetAmount'
  sortOrder?: 'asc' | 'desc'
}

// Goals Summary Interface
export interface GoalsSummary {
  totalGoals: number
  completedGoals: number
  activeGoals: number
  overdueGoals: number
  totalTargetAmount: number
  totalCurrentAmount: number
  overallProgress: number
}

// API Response Types
export interface GoalsResponse {
  data: FinancialGoal[]
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface GoalResponse {
  data: FinancialGoal
}

export interface MilestonesResponse {
  data: GoalMilestone[]
}

export interface MilestoneResponse {
  data: GoalMilestone
}

export interface GoalsSummaryResponse {
  data: GoalsSummary
}

// Form Data Types for React Hook Form
export interface GoalFormData {
  name: string
  goalType: GoalType
  targetAmount: string // String for form input, converted to number
  currentAmount: string
  initialAmount: string
  targetDate: string
  familyMemberIds: string[]
}

export interface MilestoneFormData {
  name: string
  targetAmount: string
  targetDate: string
}

export interface ProgressFormData {
  amount: string
  operation: ProgressOperation
  description: string
}

// Utility Types
export type GoalWithProgress = FinancialGoal & {
  progress: GoalProgress
}

export type GoalWithMilestones = FinancialGoal & {
  milestones: GoalMilestone[]
}

export type GoalWithMembers = FinancialGoal & {
  members: (GoalMember & {
    familyMember: {
      id: string
      name: string
      color?: string
      avatar?: string
    }
  })[]
}

// Complete Goal Type (with all relationships)
export type CompleteGoal = FinancialGoal & {
  progress: GoalProgress
  milestones: GoalMilestone[]
  members: (GoalMember & {
    familyMember: {
      id: string
      name: string
      color?: string
      avatar?: string
    }
  })[]
}
