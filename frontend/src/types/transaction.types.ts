// New Transaction Types for Installment Architecture

export interface Installment {
  id: string
  installmentNumber: number
  amount: number
  dueDate: string
  isPaid: boolean
  paidAt?: string
  description?: string
}

export interface Transaction {
  id: string
  description: string
  totalAmount: number
  totalInstallments: number
  transactionDate: string
  type: TransactionType
  accountId: string
  categoryId?: string
  destinationAccountId?: string
  exchangeRate?: number
  isFuture: boolean
  sourceCurrency?: string
  destinationCurrency?: string
  sourceAmount?: number
  destinationAmount?: number
  transferReference?: string
  createdAt: string
  updatedAt: string
  deletedAt?: string

  // Always present - installments array
  installments: Installment[]

  // Related data
  account: {
    id: string
    name: string
    type: string
    currency: string
  }
  category?: {
    id: string
    name: string
    color: string
  }
  destinationAccount?: {
    id: string
    name: string
    type: string
    currency: string
  }
  tags?: {
    id: string
    name: string
    color: string
  }[]
  familyMembers?: {
    id: string
    name: string
    color: string
  }[]
}

export type TransactionType = 'INCOME' | 'EXPENSE' | 'TRANSFER'

export type RecurrenceFrequency = 'DAILY' | 'WEEKLY' | 'BIWEEKLY' | 'MONTHLY' | 'BIANNUAL' | 'YEARLY'

export interface RecurringTransactionData {
  description: string
  fixedAmount: number
  frequency: RecurrenceFrequency
  startDate: string | Date
  endDate?: string | Date
  type: TransactionType
  accountId: string
  categoryId?: string
  tagIds?: string[]
  familyMemberId?: string
}

export interface InstallmentFormData {
  amount: number
  dueDate: string | Date
  isPaid?: boolean
  description?: string
}

export interface CreateTransactionData {
  description: string
  totalAmount: number
  transactionDate: string | Date
  type: TransactionType
  accountId: string
  categoryId?: string
  destinationAccountId?: string
  exchangeRate?: number
  isFuture?: boolean
  sourceCurrency?: string
  destinationCurrency?: string
  sourceAmount?: number
  destinationAmount?: number
  transferReference?: string
  installments?: InstallmentFormData[]
  tagIds?: string[]
  familyMemberIds?: string[]
}

export interface UpdateTransactionData {
  description?: string
  totalAmount?: number
  transactionDate?: string | Date
  categoryId?: string
  destinationAccountId?: string
  exchangeRate?: number
  isFuture?: boolean
  sourceCurrency?: string
  destinationCurrency?: string
  sourceAmount?: number
  destinationAmount?: number
  transferReference?: string
  installments?: InstallmentFormData[]
  tagIds?: string[]
  familyMemberIds?: string[]
}

export interface TransactionFilters {
  description?: string
  type?: TransactionType
  accountId?: string
  categoryId?: string
  familyMemberId?: string
  tagId?: string
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  isFuture?: boolean
  includeDeleted?: boolean
  page?: number
  limit?: number
  sortBy?: 'transactionDate' | 'totalAmount' | 'description' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface TransactionSummary {
  totalIncome: number
  totalExpenses: number
  totalTransfers: number
  netAmount: number
}

export interface PaginatedTransactionsResponse {
  data: Transaction[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  summary?: TransactionSummary
}

export interface TransactionStats {
  totalTransactions: number
  totalIncome: number
  totalExpenses: number
  netAmount: number
  averageTransaction: number
  thisMonthTransactions: number
  lastMonthTransactions: number
  growthPercentage: number
}

// Expanded transaction row for table display (one row per installment)
export interface ExpandedTransactionRow {
  // Unique row ID
  id: string

  // Original transaction data
  transactionId: string
  description: string
  totalAmount: number
  totalInstallments: number
  transactionDate: string
  type: TransactionType
  accountId: string
  categoryId?: string
  destinationAccountId?: string
  exchangeRate?: number
  isFuture: boolean
  createdAt: string
  updatedAt: string

  // Specific installment data
  installmentId: string
  installmentNumber: number
  installmentAmount: number
  installmentDueDate: string
  installmentIsPaid: boolean
  installmentPaidAt?: string
  installmentDescription?: string

  // Related data (same as transaction)
  account: {
    id: string
    name: string
    type: string
    currency: string
  }
  category?: {
    id: string
    name: string
    color: string
  }
  destinationAccount?: {
    id: string
    name: string
    type: string
    currency: string
  }
  tags?: {
    id: string
    name: string
    color: string
  }[]
  familyMembers?: {
    id: string
    name: string
    color: string
  }[]
}

// Form validation types
export interface TransactionFormData {
  description: string
  totalAmount: string
  transactionDate: string
  type: TransactionType
  accountId: string
  categoryId: string
  destinationAccountId: string
  exchangeRate: string
  isFuture: boolean
  sourceCurrency: string
  destinationCurrency: string
  sourceAmount: string
  destinationAmount: string
  transferReference: string
  installments: InstallmentFormData[]
  tagIds: string[]
  familyMemberIds: string[]
  // Recurring transaction fields
  isRecurring?: boolean
  recurrenceFrequency?: RecurrenceFrequency
  recurrenceEndDate?: string
}

// Installment-specific types
export interface InstallmentListItem {
  id: string
  transactionId: string
  transactionDescription: string
  installmentNumber: number
  totalInstallments: number
  amount: number
  dueDate: string
  isPaid: boolean
  paidAt?: string
  description?: string
  account: {
    id: string
    name: string
    type: string
  }
  category?: {
    id: string
    name: string
    color: string
  }
}

export interface InstallmentFilters {
  transactionId?: string
  isPaid?: boolean
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  accountId?: string
  categoryId?: string
  page?: number
  limit?: number
  sortBy?: 'dueDate' | 'amount' | 'installmentNumber'
  sortOrder?: 'asc' | 'desc'
}

// Constants
export const TRANSACTION_TYPES = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
  TRANSFER: 'TRANSFER'
} as const

export const TRANSACTION_TYPE_LABELS = {
  INCOME: 'Receita',
  EXPENSE: 'Despesa',
  TRANSFER: 'Transferência'
} as const

export const TRANSACTION_TYPE_COLORS = {
  INCOME: 'text-green-600',
  EXPENSE: 'text-red-600',
  TRANSFER: 'text-blue-600'
} as const

export const RECURRENCE_FREQUENCIES = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  BIWEEKLY: 'BIWEEKLY',
  MONTHLY: 'MONTHLY',
  BIANNUAL: 'BIANNUAL',
  YEARLY: 'YEARLY'
} as const

export const RECURRENCE_FREQUENCY_LABELS = {
  DAILY: 'Diário',
  WEEKLY: 'Semanal',
  BIWEEKLY: 'Quinzenal',
  MONTHLY: 'Mensal',
  BIANNUAL: 'Semestral',
  YEARLY: 'Anual'
} as const

export const SORT_OPTIONS = [
  { value: 'transactionDate', label: 'Data da Transação' },
  { value: 'totalAmount', label: 'Valor Total' },
  { value: 'description', label: 'Descrição' },
  { value: 'createdAt', label: 'Data de Criação' }
] as const

export const SORT_ORDER_OPTIONS = [
  { value: 'desc', label: 'Decrescente' },
  { value: 'asc', label: 'Crescente' }
] as const

export const INSTALLMENT_SORT_OPTIONS = [
  { value: 'dueDate', label: 'Data de Vencimento' },
  { value: 'amount', label: 'Valor' },
  { value: 'installmentNumber', label: 'Número da Parcela' }
] as const

// Helper functions
export const isInstallmentTransaction = (transaction: Transaction): boolean => {
  return transaction.totalInstallments > 1
}

export const getInstallmentStatus = (installment: Installment): 'paid' | 'overdue' | 'pending' => {
  if (installment.isPaid) return 'paid'
  
  const dueDate = new Date(installment.dueDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  return dueDate < today ? 'overdue' : 'pending'
}

export const calculateTransactionProgress = (transaction: Transaction): {
  paidInstallments: number
  totalInstallments: number
  percentage: number
  paidAmount: number
  remainingAmount: number
} => {
  const paidInstallments = transaction.installments.filter(i => i.isPaid).length
  const paidAmount = transaction.installments
    .filter(i => i.isPaid)
    .reduce((sum, i) => sum + i.amount, 0)
  
  return {
    paidInstallments,
    totalInstallments: transaction.totalInstallments,
    percentage: (paidInstallments / transaction.totalInstallments) * 100,
    paidAmount,
    remainingAmount: transaction.totalAmount - paidAmount
  }
}
